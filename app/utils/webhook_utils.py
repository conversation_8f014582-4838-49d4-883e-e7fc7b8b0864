import httpx
import json
from datetime import datetime
from typing import Dict, Any

from app.config import settings
from app.database import get_session
from app.models.db_models import MccAnalysis
from app.utils.logger import ConsoleLogger


def test_webhook_endpoints(logger: ConsoleLogger) -> Dict[str, Any]:
    """
    Test both webhook endpoints to verify they are reachable and working
    
    Args:
        logger (ConsoleLogger): Logger instance
        
    Returns:
        Dict[str, Any]: Test results for both endpoints
    """
    logger.info("Starting webhook endpoints test")
    
    headers = {
        "X-API-KEY": settings.BIZTEL_API_KEY,
        "Content-Type": "application/json"
    }
    results = {
        "mcc_endpoint": {"reachable": False, "error": None},
        "policy_endpoint": {"reachable": False, "error": None},
        "config": {
            "base_url": settings.BASE_URL,
            "api_key_configured": bool(settings.BIZTEL_API_KEY)
        }
    }
    
    # Test MCC endpoint
    mcc_url = f"{settings.BASE_URL}/api/mcc/results"
    logger.info(f"Testing MCC endpoint: {mcc_url}")
    
    test_mcc_data = {
        "scrape_request_ref_id": "webhook_test_mcc_123",
        "mcc": 5942,
        "businessCategory": "Test Category",
        "businessDescription": "Test Description",
        "status": "COMPLETED"
    }
    
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.patch(mcc_url, json=test_mcc_data, headers=headers)
            results["mcc_endpoint"]["reachable"] = True
            results["mcc_endpoint"]["status_code"] = response.status_code
            results["mcc_endpoint"]["response"] = response.text
            logger.info(f"MCC endpoint test - Status: {response.status_code}")
    except Exception as e:
        results["mcc_endpoint"]["error"] = str(e)
        logger.error(f"MCC endpoint test failed: {str(e)}")
    
    # Test Policy endpoint
    policy_url = f"{settings.BASE_URL}/api/policy/results"
    logger.info(f"Testing Policy endpoint: {policy_url}")
    
    test_policy_data = {
        "scrape_request_ref_id": "webhook_test_policy_123",
        "status": "COMPLETED",
        "policy_details": {"test": True}
    }
    
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.patch(policy_url, json=test_policy_data, headers=headers)
            results["policy_endpoint"]["reachable"] = True
            results["policy_endpoint"]["status_code"] = response.status_code
            results["policy_endpoint"]["response"] = response.text
            logger.info(f"Policy endpoint test - Status: {response.status_code}")
    except Exception as e:
        results["policy_endpoint"]["error"] = str(e)
        logger.error(f"Policy endpoint test failed: {str(e)}")
    
    logger.info("Webhook endpoints test completed", data=results)
    return results


def exit_process(policy_result: Dict[str, Any], mcc_result: Dict[str, Any], analysis_id: int, status: str, logger: ConsoleLogger):
    """
    Exit process function that updates database and sends webhook notifications with comprehensive error handling
    
    Args:
        policy_result (Dict[str, Any]): Policy analysis results
        mcc_result (Dict[str, Any]): MCC analysis results
        analysis_id (int): Analysis ID for database updates
        status (str): Final status of the process
        logger (ConsoleLogger): Logger instance for logging
    """
    logger.info(
        "🚀 Starting exit_process function",
        {"policy_result": policy_result, "mcc_result": mcc_result, "status": status, "analysis_id": analysis_id},
    )

    # Validate inputs with defaults
    try:
        if not isinstance(policy_result, dict):
            logger.warning(f"Invalid policy_result type: {type(policy_result)}, using empty dict")
            policy_result = {}
        
        if not isinstance(mcc_result, dict):
            logger.warning(f"Invalid mcc_result type: {type(mcc_result)}, using empty dict")
            mcc_result = {}
        
        if not analysis_id or not isinstance(analysis_id, int) or analysis_id <= 0:
            logger.error(f"Invalid analysis_id: {analysis_id}")
            return  # Cannot proceed without valid analysis_id
        
        if not status or not isinstance(status, str):
            logger.warning(f"Invalid status: {status}, defaulting to 'FAILED'")
            status = "FAILED"
            
    except Exception as validation_error:
        logger.error(f"Error validating inputs: {str(validation_error)}")
        return

    # First update the database status
    logger.info("📊 Updating database status...")
    try:
        with next(get_session()) as session:
            try:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    # Update MCC code - handle both integer and string with proper validation
                    try:
                        mcc_value = mcc_result.get("mcc", -1)
                        if isinstance(mcc_value, int) and mcc_value != -1:
                            analysis.mcc_code = str(mcc_value)
                        elif isinstance(mcc_value, str) and mcc_value.strip():
                            analysis.mcc_code = mcc_value.strip()
                        else:
                            analysis.mcc_code = "-1"  # Default fallback
                    except Exception as mcc_code_error:
                        logger.warning(f"Error setting MCC code: {str(mcc_code_error)}, using default")
                        analysis.mcc_code = "-1"
                    
                    # Update business category with validation
                    try:
                        business_category = mcc_result.get("businessCategory", "")
                        if isinstance(business_category, str):
                            analysis.business_category = business_category
                        else:
                            analysis.business_category = str(business_category) if business_category else ""
                    except Exception as category_error:
                        logger.warning(f"Error setting business category: {str(category_error)}")
                        analysis.business_category = ""
                    
                    # Update business description with validation
                    try:
                        business_description = mcc_result.get("businessDescription", "")
                        if isinstance(business_description, str):
                            analysis.business_description = business_description
                        else:
                            analysis.business_description = str(business_description) if business_description else ""
                    except Exception as description_error:
                        logger.warning(f"Error setting business description: {str(description_error)}")
                        analysis.business_description = ""
                    
                    # Update status fields with proper validation
                    try:
                        analysis.result_status = status
                        analysis.processing_status = "COMPLETED" if status == "COMPLETED" else "FAILED"
                        
                        current_timestamp = datetime.now().isoformat() + "Z"
                        if status == "COMPLETED":
                            analysis.completed_at = current_timestamp
                            analysis.failed_at = None
                        else:
                            analysis.failed_at = current_timestamp
                            analysis.completed_at = None

                        analysis.last_updated = current_timestamp
                        
                    except Exception as status_error:
                        logger.warning(f"Error setting status fields: {str(status_error)}")
                        # Set minimal required fields
                        analysis.result_status = status
                        analysis.last_updated = datetime.now().isoformat() + "Z"
                    
                    # Commit changes
                    try:
                        session.commit()
                        logger.info(
                            "✅ Database updated successfully",
                            {
                                "mcc_code": getattr(analysis, 'mcc_code', 'unknown'),
                                "business_category": getattr(analysis, 'business_category', 'unknown'),
                                "status": status,
                            },
                        )
                    except Exception as commit_error:
                        logger.error(f"Error committing database changes: {str(commit_error)}")
                        try:
                            session.rollback()
                        except Exception as rollback_error:
                            logger.error(f"Error rolling back database changes: {str(rollback_error)}")
                        # Don't return here, still try to send webhooks
                        
                else:
                    logger.warning(f"⚠️ No analysis found with ID: {analysis_id}")
                    
            except Exception as session_error:
                logger.error(f"Error in database session operations: {str(session_error)}")
                try:
                    session.rollback()
                except Exception:
                    pass
                
    except Exception as db_error:
        logger.error("❌ Error updating analysis status in database", error=db_error)
        # Don't raise here, we still want to try sending webhooks

    # Then send webhooks using PATCH calls
    try:
        # Validate webhook configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)
        
        if not base_url:
            logger.error("BASE_URL not configured, cannot send webhooks")
            return
            
        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send webhooks")
            return
        
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        
        # Log configuration details
        logger.info(
            "Webhook configuration",
            {
                "base_url": base_url,
                "api_key_configured": bool(api_key),
                "api_key_length": len(api_key) if api_key else 0
            }
        )
        
    except Exception as config_error:
        logger.error(f"Error validating webhook configuration: {str(config_error)}")
        return
    
    # Send MCC webhook
    try:
        mcc_webhook_url = f"{base_url}/api/mcc/results"
        logger.info(
            "Preparing to send MCC webhook",
            {
                "url": mcc_webhook_url,
                "method": "PATCH",
                "payload_keys": list(mcc_result.keys()) if mcc_result else [],
                "analysis_id": analysis_id
            }
        )
        
        try:
            logger.info("Starting MCC PATCH request...")
            
            with httpx.Client(timeout=30.0) as client:
                # Log request details
                logger.info(
                    "Making MCC PATCH request",
                    {
                        "url": mcc_webhook_url,
                        "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in headers.items()},
                        "payload_size": len(str(mcc_result)) if mcc_result else 0,
                        "payload": mcc_result  # Full payload for debugging
                    }
                )
                
                response = client.patch(mcc_webhook_url, json=mcc_result, headers=headers)
                
                # Log response details
                logger.info(
                    "MCC PATCH response received",
                    {
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_headers": dict(response.headers),
                        "url": mcc_webhook_url
                    }
                )
                
                if response.status_code == 200:
                    logger.info("✅ MCC webhook sent successfully")
                else:
                    logger.error(
                        "❌ MCC webhook failed with non-200 status",
                        data={
                            "status_code": response.status_code,
                            "response": response.text,
                            "url": mcc_webhook_url
                        }
                    )
                    
        except httpx.TimeoutException as e:
            logger.error(
                "❌ MCC webhook timed out",
                {
                    "url": mcc_webhook_url,
                    "timeout": 30.0,
                    "error": str(e)
                }
            )
        except httpx.ConnectError as e:
            logger.error(
                "❌ MCC webhook connection failed",
                {
                    "url": mcc_webhook_url,
                    "error": str(e)
                }
            )
        except Exception as e:
            logger.error(
                "❌ MCC webhook exception occurred",
                {
                    "url": mcc_webhook_url,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
    except Exception as mcc_webhook_error:
        logger.error(f"Error in MCC webhook preparation: {str(mcc_webhook_error)}")

    # Send Policy webhook
    try:
        policy_webhook_url = f"{base_url}/api/policy/results"

        # Create policy-specific headers (simplified - no Basic auth)
        policy_headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }

        logger.info(
            "Preparing to send Policy webhook",
            {
                "url": policy_webhook_url,
                "method": "PATCH",
                "payload_keys": list(policy_result.keys()) if policy_result else [],
                "analysis_id": analysis_id,
                "headers": "simplified (X-API-KEY + Content-Type only)"
            }
        )

        try:
            logger.info("Starting Policy PATCH request...")

            with httpx.Client(timeout=30.0) as client:
                # Log request details
                logger.info(
                    "Making Policy PATCH request",
                    {
                        "url": policy_webhook_url,
                        "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in policy_headers.items()},
                        "payload_size": len(str(policy_result)) if policy_result else 0,
                        "payload": policy_result  # Full payload for debugging
                    }
                )

                response = client.patch(policy_webhook_url, json=policy_result, headers=policy_headers)
                
                # Log response details
                logger.info(
                    "Policy PATCH response received",
                    {
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_headers": dict(response.headers),
                        "url": policy_webhook_url
                    }
                )
                
                if response.status_code == 200:
                    logger.info("✅ Policy webhook sent successfully")
                else:
                    logger.error(
                        "❌ Policy webhook failed with non-200 status",
                        data={
                            "status_code": response.status_code,
                            "response": response.text,
                            "url": policy_webhook_url
                        }
                    )
                    
        except httpx.TimeoutException as e:
            logger.error(
                "❌ Policy webhook timed out",
                {
                    "url": policy_webhook_url,
                    "timeout": 30.0,
                    "error": str(e)
                }
            )
        except httpx.ConnectError as e:
            logger.error(
                "❌ Policy webhook connection failed",
                {
                    "url": policy_webhook_url,
                    "error": str(e)
                }
            )
        except Exception as e:
            logger.error(
                "❌ Policy webhook exception occurred",
                {
                    "url": policy_webhook_url,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
    except Exception as policy_webhook_error:
        logger.error(f"Error in Policy webhook preparation: {str(policy_webhook_error)}")

    logger.info("Exit process completed")


def send_webhook_notification(webhook_url: str, data: Dict[str, Any], webhook_type: str, logger: ConsoleLogger) -> bool:
    """
    Send a webhook notification with comprehensive logging
    
    Args:
        webhook_url (str): The webhook URL to send to
        data (Dict[str, Any]): Data to send in the webhook
        webhook_type (str): Type of webhook (for logging)
        logger (ConsoleLogger): Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    headers = {
        "X-API-KEY": settings.BIZTEL_API_KEY,
        "Content-Type": "application/json"
    }
    
    logger.info(
        f"Preparing {webhook_type} webhook notification",
        {
            "url": webhook_url,
            "method": "PATCH",
            "data_keys": list(data.keys()) if data else [],
            "payload_size": len(str(data)) if data else 0
        }
    )
    
    try:
        logger.info(f"Starting {webhook_type} PATCH request...")
        
        with httpx.Client(timeout=30.0) as client:
            # Log request details
            logger.info(
                f"Making {webhook_type} PATCH request",
                {
                    "url": webhook_url,
                    "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in headers.items()},
                    "payload": data
                }
            )
            
            response = client.patch(webhook_url, json=data, headers=headers)
            
            # Log response details
            logger.info(
                f"{webhook_type} PATCH response received",
                {
                    "status_code": response.status_code,
                    "response_text": response.text,
                    "response_headers": dict(response.headers),
                    "url": webhook_url
                }
            )
            
            if response.status_code == 200:
                logger.info(f"✅ {webhook_type} webhook sent successfully")
                return True
            else:
                logger.error(
                    f"❌ {webhook_type} webhook failed with non-200 status",
                    data={
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": webhook_url
                    }
                )
                return False
                
    except httpx.TimeoutException as e:
        logger.error(
            f"❌ {webhook_type} webhook timed out",
            {
                "url": webhook_url,
                "timeout": 30.0,
                "error": str(e)
            }
        )
        return False
    except httpx.ConnectError as e:
        logger.error(
            f"❌ {webhook_type} webhook connection failed",
            {
                "url": webhook_url,
                "error": str(e)
            }
        )
        return False
    except Exception as e:
        logger.error(
            f"❌ {webhook_type} webhook exception occurred",
            {
                "url": webhook_url,
                "error": str(e),
                "error_type": type(e).__name__
            }
        )
        return False


def send_mcc_results_webhook(
    scrape_request_ref_id: str, 
    website: str, 
    mcc_result: Dict[str, Any], 
    analysis_id: int,
    logger: ConsoleLogger
) -> bool:
    """
    Send MCC results to external API using the correct data format
    
    Args:
        scrape_request_ref_id (str): The scrape request reference ID (used as scrapeRequestUuid)
        website (str): The website URL
        mcc_result (Dict[str, Any]): MCC analysis results
        analysis_id (int): Analysis ID for logging
        logger (ConsoleLogger): Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    logger.info(
        "🚀 Preparing MCC results webhook with correct format",
        {
            "scrape_request_ref_id": scrape_request_ref_id,
            "website": website,
            "analysis_id": analysis_id
        }
    )
    
    try:
        # Validate configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)
        
        if not base_url:
            logger.error("BASE_URL not configured, cannot send MCC webhook")
            return False
            
        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send MCC webhook")
            return False
        
        # Prepare headers - only X-API-KEY is required
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        mcc_webhook_url = f"{base_url}/api/mcc/results"
        
        # Extract data with proper defaults and error handling
        try:
            mcc_code = mcc_result.get("mcc", -1) if mcc_result else -1
            business_category = mcc_result.get("businessCategory", "") if mcc_result else ""
            business_description = mcc_result.get("businessDescription", "") if mcc_result else ""
            status = mcc_result.get("status", "COMPLETED") if mcc_result else "COMPLETED"
        except Exception as extract_error:
            logger.error(f"Error extracting MCC data: {extract_error}")
            mcc_code = -1
            business_category = ""
            business_description = ""
            status = "COMPLETED"
        
        # Format data according to expected specification with error handling
        try:
            webhook_payload = {
                "website": website if website else "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",  # ISO format with Z suffix
                "status": status,
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "mcc": int(mcc_code) if isinstance(mcc_code, (int, str)) and str(mcc_code).isdigit() else -1,
                "manualMcc": -1,  # Default value as per specification
                "businessCategory": str(business_category) if business_category else "",
                "businessDescription": str(business_description) if business_description else ""
            }
            
            # 🚀 COMPREHENSIVE MCC PAYLOAD CONSTRUCTION LOGGING 
            logger.info(
                "📦 MCC WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP",
                {
                    "construction_step": "1_mcc_payload_created",
                    "timestamp": datetime.now().isoformat(),
                    "website": website,
                    "scrape_request_ref_id": scrape_request_ref_id,
                    "mcc_code": mcc_code,
                    "business_category": business_category,
                    "business_description_length": len(business_description) if business_description else 0,
                    "payload_keys": list(webhook_payload.keys()),
                    "payload_structure": {
                        "website": webhook_payload.get("website"),
                        "scrapeRequestUuid": webhook_payload.get("scrapeRequestUuid"),
                        "status": webhook_payload.get("status"),
                        "mcc": webhook_payload.get("mcc"),
                        "manualMcc": webhook_payload.get("manualMcc"),
                        "businessCategory": webhook_payload.get("businessCategory"),
                        "businessDescription_length": len(webhook_payload.get("businessDescription", "")),
                        "createdDate": webhook_payload.get("createdDate")
                    },
                    "complete_payload_json": webhook_payload  # 🔍 COMPLETE PAYLOAD FOR DEBUGGING
                }
            )
        except Exception as payload_error:
            logger.error(f"Error creating MCC webhook payload: {payload_error}")
            # Create completely default payload
            webhook_payload = {
                "website": "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "COMPLETED",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "mcc": -1,
                "manualMcc": -1,
                "businessCategory": "",
                "businessDescription": ""
            }
        
        logger.info(
            "📤 Sending MCC PATCH request with clean headers - DETAILED DEBUG",
            {
                "url": mcc_webhook_url,
                "method": "PATCH",
                "payload": webhook_payload,
                "headers_being_sent": headers,  # Show actual headers for comparison
                "api_key_in_headers": headers.get("X-API-KEY"),
                "content_type_in_headers": headers.get("Content-Type"),
                "api_key_length": len(headers.get("X-API-KEY", "")) if headers.get("X-API-KEY") else 0
            }
        )
        
        # Send the PATCH request
        with httpx.Client(timeout=30.0) as client:
            # 🚀 FINAL MCC REQUEST LOGGING - EXACTLY WHAT'S BEING SENT
            logger.info(
                "🔥 MCC PATCH REQUEST - FINAL TRANSMISSION",
                {
                    "construction_step": "2_sending_mcc_request",
                    "timestamp": datetime.now().isoformat(),
                    "about_to_send": True,
                    "final_url": mcc_webhook_url,
                    "final_method": "PATCH",
                    "final_headers": dict(headers),
                    "final_payload": webhook_payload,
                    "final_timeout": 30.0,
                    "curl_equivalent": f"curl -X PATCH '{mcc_webhook_url}' " + 
                                      f"-H 'Content-Type: {headers.get('Content-Type', 'application/json')}' " +
                                      f"-H 'X-API-KEY: {headers.get('X-API-KEY', 'MISSING')}' " +
                                      f"-d '{json.dumps(webhook_payload)}'"
                }
            )
            
            response = client.patch(mcc_webhook_url, json=webhook_payload, headers=headers)
            
            # 🚀 IMMEDIATE MCC RESPONSE LOGGING
            logger.info(
                "⚡ MCC PATCH RESPONSE - IMMEDIATE RESULT",
                {
                    "construction_step": "3_mcc_response_received",
                    "timestamp": datetime.now().isoformat(),
                    "response_status_code": response.status_code,
                    "response_headers": dict(response.headers),
                    "response_text": response.text,
                    "response_success": response.status_code == 200,
                    "response_size": len(response.text) if response.text else 0,
                    "request_url": mcc_webhook_url
                }
            )
            
            # Log response details
            logger.info(
                "📥 MCC PATCH response received",
                {
                    "status_code": response.status_code,
                    "response_text": response.text,
                    "response_headers": dict(response.headers),
                    "url": mcc_webhook_url
                }
            )
            
            if response.status_code == 200:
                logger.info("✅ MCC results webhook sent successfully")
                return True
            elif response.status_code == 401:
                logger.error(
                    "❌ MCC results webhook failed - Authentication error",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": mcc_webhook_url,
                        "error": "Invalid API key - check BIZTEL_API_KEY in .env file",
                        "api_key_length": len(api_key) if api_key else 0
                    }
                )
                # Try to send with default values even if authentication fails
                logger.info("🔄 Attempting to send default values despite authentication error")
                try:
                    default_payload = {
                        "website": website if website else "insufficient data",
                        "createdDate": datetime.now().isoformat() + "Z",
                        "status": "FAILED",
                        "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                        "mcc": -1,
                        "manualMcc": -1,
                        "businessCategory": "authentication_error",
                        "businessDescription": "webhook authentication failed"
                    }
                    logger.info("📋 Created default payload for authentication error", {"payload": default_payload})
                except Exception as default_error:
                    logger.error(f"Failed to create default payload: {default_error}")
                return False
            else:
                logger.error(
                    "❌ MCC results webhook failed with non-200 status",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": mcc_webhook_url,
                        "payload": webhook_payload
                    }
                )
                return False
                
    except httpx.TimeoutException as e:
        logger.error(
            "❌ MCC results webhook timed out",
            {
                "url": mcc_webhook_url,
                "timeout": 30.0,
                "error": str(e)
            }
        )
        return False
    except httpx.ConnectError as e:
        logger.error(
            "❌ MCC results webhook connection failed",
            {
                "url": mcc_webhook_url,
                "error": str(e)
            }
        )
        return False
    except Exception as e:
        logger.error(
            "❌ MCC results webhook exception occurred",
            {
                "url": mcc_webhook_url,
                "error": str(e),
                "error_type": type(e).__name__,
                "payload": webhook_payload if 'webhook_payload' in locals() else None
            }
        )

        # Always try to create and log default values even if webhook completely fails
        try:
            logger.info("🔄 Creating default webhook payload after complete failure")
            default_payload = {
                "website": website if website else "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "FAILED",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "mcc": -1,
                "manualMcc": -1,
                "businessCategory": "webhook_error",
                "businessDescription": f"webhook failed: {str(e)[:100]}"
            }
            logger.info("📋 Default payload created for failed webhook", {"payload": default_payload})
        except Exception as default_error:
            logger.error(f"Failed to create default payload after webhook error: {default_error}")

        return False


def _truncate_text_to_words(text: str, max_words: int) -> str:
    """
    Truncate text to a maximum number of words for API compatibility

    Args:
        text (str): Original text to truncate
        max_words (int): Maximum number of words to keep

    Returns:
        str: Truncated text or original if within limit
    """
    if not text or text == "":
        return text

    words = text.split()
    if len(words) <= max_words:
        return text

    truncated = " ".join(words[:max_words])
    return truncated

def send_policy_results_webhook(
    scrape_request_ref_id: str,
    website: str,
    policy_result: Dict[str, Any],
    analysis_id: int,
    logger: ConsoleLogger
) -> bool:
    """
    Send policy analysis results to external webhook endpoint

    Args:
        scrape_request_ref_id (str): Scrape request reference ID
        website (str): Website URL
        policy_result (Dict[str, Any]): Policy analysis results
        analysis_id (int): Analysis ID
        logger (ConsoleLogger): Logger instance

    Returns:
        bool: True if webhook sent successfully, False otherwise
    """
    try:
        # Validate webhook configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)

        if not base_url:
            logger.error("BASE_URL not configured, cannot send policy webhook")
            return False

        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send policy webhook")
            return False

        # Prepare webhook URL and headers - POLICY ANALYSIS SPECIFIC (simplified headers)
        policy_webhook_url = f"{base_url}/api/policy/results"
        policy_headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        # Note: Policy analysis webhook uses simplified headers (no Basic auth)

        # Log configuration details with FULL header information for debugging
        logger.info(
            "📤 Policy webhook configuration",
            {
                "base_url": base_url,
                "webhook_url": policy_webhook_url,
                "api_key_configured": bool(api_key),
                "api_key_length": len(api_key) if api_key else 0,
                "api_key_value": api_key,  # TEMPORARY: Show actual API key for debugging
                "scrape_request_ref_id": scrape_request_ref_id,
                "analysis_id": analysis_id,
                "headers_to_send": policy_headers  # TEMPORARY: Show actual policy headers for debugging
            }
        )

        # Prepare webhook payload with size optimization
        policies = policy_result.get("policies", [])

        # Validate and clean policies payload to ensure Azure URLs are used
        validated_policies = []

        # Ensure policies is a list
        if not isinstance(policies, list):
            logger.warning("Policies is not a list, converting to empty list")
            policies = []

        for policy in policies:
            try:
                validated_policy = policy.copy() if isinstance(policy, dict) else {}

                # Ensure all required fields exist with defaults
                validated_policy["type"] = validated_policy.get("type", "UNKNOWN")
                validated_policy["url"] = validated_policy.get("url", "insufficient data")

                # Truncate text to maximum 100 words for API compatibility
                original_text = validated_policy.get("text", "insufficient data")
                truncated_text = _truncate_text_to_words(original_text, 100)

                if truncated_text != original_text:
                    logger.info(
                        "📝 Text truncated to 100 words for API compatibility",
                        {
                            "policy_type": validated_policy.get("type", "unknown"),
                            "original_words": len(original_text.split()) if isinstance(original_text, str) else 0,
                            "truncated_words": len(truncated_text.split()) if isinstance(truncated_text, str) else 0,
                            "truncation_reason": "api_100_word_limit"
                        }
                    )

                validated_policy["text"] = truncated_text

                validated_policy["imglink"] = validated_policy.get("imglink", "insufficient data")

                # Validate imglink field - should be Azure URL, not base64 data
                imglink = validated_policy["imglink"]
                if isinstance(imglink, str):
                    if imglink.startswith("data:image/"):
                        # This should not happen with the new implementation
                        logger.error(
                            "❌ Base64 image data found in webhook payload - this should be an Azure URL",
                            {
                                "policy_type": validated_policy.get("type", "unknown"),
                                "imglink_preview": imglink[:100] + "..." if len(imglink) > 100 else imglink
                            }
                        )
                        # Set to insufficient data instead of sending base64
                        validated_policy["imglink"] = "insufficient data"
                    elif imglink.startswith("https://") and "blob.core.windows.net" in imglink:
                        # Valid Azure URL
                        logger.debug(
                            "✅ Valid Azure URL found in policy",
                            {
                                "policy_type": validated_policy.get("type", "unknown"),
                                "azure_url": imglink
                            }
                        )
                    elif imglink == "insufficient data":
                        # Expected fallback value
                        pass
                    else:
                        logger.warning(
                            "⚠️ Unexpected imglink format in policy, setting to insufficient data",
                            {
                                "policy_type": validated_policy.get("type", "unknown"),
                                "imglink": imglink
                            }
                        )
                        validated_policy["imglink"] = ""
                else:
                    # Non-string imglink, set to default
                    validated_policy["imglink"] = ""

                validated_policies.append(validated_policy)

            except Exception as policy_error:
                logger.error(f"Error validating policy: {policy_error}")
                # Add a default policy entry
                validated_policies.append({
                    "type": "UNKNOWN",
                    "url": "",
                    "imglink": "",
                    "text": ""
                })

        # Prepare webhook payload with guaranteed defaults
        try:
            # Convert org_id to integer as required by Java server
            org_id_value = policy_result.get("org_id", "default") if policy_result else "default"
            try:
                # Try to convert org_id to integer, fallback to 1 if conversion fails
                org_id_int = int(org_id_value) if org_id_value and org_id_value != "default" else 1
            except (ValueError, TypeError):
                logger.warning(f"Failed to convert org_id '{org_id_value}' to integer, using default value 1")
                org_id_int = 1

            # Fix createdDate format - ensure it's always ISO string format
            created_date_raw = policy_result.get("createdDate") if policy_result else None
            if created_date_raw:
                # Handle different datetime formats
                if isinstance(created_date_raw, str):
                    # Already a string, check if it's in ISO format
                    if created_date_raw.endswith('Z') or '+' in created_date_raw:
                        created_date_iso = created_date_raw
                    else:
                        # Try to parse and convert to ISO format
                        try:
                            from datetime import datetime
                            dt = datetime.fromisoformat(created_date_raw.replace('Z', '+00:00'))
                            created_date_iso = dt.isoformat() + "Z"
                        except:
                            created_date_iso = datetime.now().isoformat() + "Z"
                else:
                    # Assume it's a datetime object, convert to ISO format
                    try:
                        created_date_iso = created_date_raw.isoformat() + "Z"
                    except:
                        created_date_iso = datetime.now().isoformat() + "Z"
            else:
                created_date_iso = datetime.now().isoformat() + "Z"

            webhook_payload = {
                "website": website if website else "insufficient data",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "createdDate": created_date_iso,
                "status": policy_result.get("status", "COMPLETED") if policy_result else "COMPLETED",
                "policies": validated_policies if validated_policies else [],
                "org_id": org_id_int  # Now an integer as required by Java server
            }
            
            # 🚀 COMPREHENSIVE PAYLOAD CONSTRUCTION LOGGING 
            logger.info(
                "📦 POLICY WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP",
                {
                    "construction_step": "1_payload_created",
                    "timestamp": datetime.now().isoformat(),
                    "website": website,
                    "scrape_request_ref_id": scrape_request_ref_id,
                    "org_id": "org_id",
                    "org_id_int": org_id_int,
                    "policy_result_status": policy_result.get("status") if policy_result else None,
                    "validated_policies_count": len(validated_policies) if validated_policies else 0,
                    "payload_keys": list(webhook_payload.keys()),
                    "payload_structure": {
                        "website": webhook_payload.get("website"),
                        "scrapeRequestUuid": webhook_payload.get("scrapeRequestUuid"),
                        "status": webhook_payload.get("status"),
                        "org_id": webhook_payload.get("org_id"),
                        "policies_count": len(webhook_payload.get("policies", [])),
                        "createdDate": webhook_payload.get("createdDate")
                    }
                }
            )

            # Ensure we have at least one policy entry
            if not webhook_payload["policies"]:
                logger.warning("No policies found, adding default policy entries")
                webhook_payload["policies"] = [
                    {"type": "RAC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                ]
                
                # 🚀 POLICY FALLBACK LOGGING
                logger.info(
                    "📦 POLICY WEBHOOK PAYLOAD - FALLBACK POLICIES ADDED",
                    {
                        "construction_step": "2_fallback_policies_added",
                        "timestamp": datetime.now().isoformat(),
                        "reason": "no_policies_found",
                        "fallback_policies_count": len(webhook_payload["policies"]),
                        "fallback_policy_types": [p["type"] for p in webhook_payload["policies"]]
                    }
                )
            else:
                # 🚀 POLICY SUCCESS LOGGING
                logger.info(
                    "📦 POLICY WEBHOOK PAYLOAD - REAL POLICIES PRESENT",
                    {
                        "construction_step": "2_real_policies_present",
                        "timestamp": datetime.now().isoformat(),
                        "policies_count": len(webhook_payload["policies"]),
                        "policy_types": [p.get("type", "unknown") for p in webhook_payload["policies"]],
                        "policy_details": [
                            {
                                "type": p.get("type", "unknown"),
                                "has_url": bool(p.get("url")),
                                "has_imglink": bool(p.get("imglink")),
                                "has_text": bool(p.get("text")),
                                "url_preview": p.get("url", "")[:50] + "..." if len(p.get("url", "")) > 50 else p.get("url", ""),
                                "text_length": len(p.get("text", "")) if p.get("text") else 0
                            }
                            for p in webhook_payload["policies"]
                        ]
                    }
                )

        except Exception as payload_error:
            logger.error(f"Error creating webhook payload: {payload_error}")
            # Create completely default payload with integer org_id
            webhook_payload = {
                "website": "insufficient data",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "COMPLETED",
                "policies": [
                    {"type": "RAC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                ],
                "org_id": 1  # Integer default value for Java server compatibility
            }

        # Calculate payload size for monitoring
        payload_size = len(str(webhook_payload))
        payload_size_kb = round(payload_size / 1024, 2)
        
        # 🚀 PAYLOAD SIZE ANALYSIS LOGGING
        logger.info(
            "📏 POLICY WEBHOOK PAYLOAD - SIZE ANALYSIS",
            {
                "construction_step": "3_size_calculated",
                "timestamp": datetime.now().isoformat(),
                "payload_size_bytes": payload_size,
                "payload_size_kb": payload_size_kb,
                "is_large_payload": payload_size_kb > 10,
                "size_warning": "payload_too_large" if payload_size_kb > 10 else "payload_size_ok",
                "final_policies_count": len(webhook_payload.get("policies", [])),
                "complete_payload_json": webhook_payload  # 🔍 COMPLETE PAYLOAD FOR DEBUGGING
            }
        )

        # Count Azure URLs vs insufficient data and text truncations
        azure_url_count = 0
        insufficient_data_count = 0
        text_truncated_count = 0
        total_original_text_length = 0
        total_truncated_text_length = 0

        for policy in validated_policies:
            imglink = policy.get("imglink", "")
            if imglink.startswith("https://") and "blob.core.windows.net" in imglink:
                # Check if it's a real Azure URL or a placeholder
                if "placeholder_" in imglink:
                    insufficient_data_count += 1  # Count placeholders as insufficient data
                else:
                    azure_url_count += 1
            elif imglink == "":
                insufficient_data_count += 1

            # Track text truncation statistics
            text = policy.get("text", "")
            if isinstance(text, str) and "[truncated for webhook]" in text:
                text_truncated_count += 1
                # Estimate original length (truncated text + truncation message length)
                truncated_length = len(text) - len("... [truncated for webhook]")
                total_truncated_text_length += len(text)
                # This is an approximation since we don't store the original length
                total_original_text_length += truncated_length * 10  # Rough estimate
            else:
                total_original_text_length += len(text) if isinstance(text, str) else 0
                total_truncated_text_length += len(text) if isinstance(text, str) else 0

        # Validate payload size - warn if still too large after truncation
        if payload_size_kb > 10:
            logger.warning(
                "⚠️ Webhook payload still large after truncation - may cause server errors",
                {
                    "payload_size_kb": payload_size_kb,
                    "target_size_kb": 10,
                    "policies_count": len(validated_policies),
                    "text_truncated_count": text_truncated_count,
                    "suggestion": "Consider reducing text truncation limit or removing more content"
                }
            )
        elif payload_size_kb > 5:
            logger.info(
                "📏 Webhook payload size acceptable but approaching limit",
                {
                    "payload_size_kb": payload_size_kb,
                    "target_size_kb": 10,
                    "status": "acceptable"
                }
            )
        else:
            logger.info(
                "✅ Webhook payload size optimized successfully",
                {
                    "payload_size_kb": payload_size_kb,
                    "target_size_kb": 10,
                    "status": "optimal"
                }
            )

        logger.info(
            "📋 Policy webhook payload prepared with text truncation optimization",
            {
                "website": website,
                "scrape_request_ref_id": scrape_request_ref_id,
                "policies_count": len(webhook_payload["policies"]),
                "azure_urls": azure_url_count,
                "insufficient_data": insufficient_data_count,
                "text_truncated_count": text_truncated_count,
                "estimated_original_text_length": total_original_text_length,
                "final_text_length": total_truncated_text_length,
                "estimated_size_reduction_bytes": total_original_text_length - total_truncated_text_length,
                "status": webhook_payload["status"],
                "org_id": webhook_payload["org_id"],
                "payload_size_bytes": payload_size,
                "payload_size_kb": payload_size_kb,
                "size_optimization_status": "optimal" if payload_size_kb <= 5 else "acceptable" if payload_size_kb <= 10 else "warning"
            }
        )

        # Log detailed request information before sending
        logger.info(
            "🚀 About to send Policy PATCH request - DETAILED DEBUG",
            {
                "url": policy_webhook_url,
                "method": "PATCH",
                "headers_being_sent": policy_headers,  # Show actual policy-specific headers
                "api_key_in_headers": policy_headers.get("X-API-KEY"),
                "content_type_in_headers": policy_headers.get("Content-Type"),
                "payload_size_bytes": payload_size,
                "payload_size_kb": payload_size_kb,
                "timeout_seconds": 60.0 if payload_size > 50000 else 30.0,
                "webhook_stage": "pre_send",
                "optimization_applied": text_truncated_count > 0,
                "expected_outcome": "success" if payload_size_kb <= 10 else "potential_500_error"
            }
        )

        # Send the PATCH request with increased timeout for large payloads
        timeout_seconds = 60.0 if payload_size > 50000 else 30.0

        # Enable httpx logging to see the actual request
        import logging
        httpx_logger = logging.getLogger("httpx")
        original_level = httpx_logger.level
        httpx_logger.setLevel(logging.DEBUG)

        try:
            with httpx.Client(timeout=timeout_seconds) as client:
                # Enhanced request tracking - log exact payload and headers being sent
                logger.info(
                    "📡 POLICY WEBHOOK REQUEST TRACKING - DETAILED DEBUG",
                    {
                        "client_timeout": timeout_seconds,
                        "request_url": policy_webhook_url,
                        "request_method": "PATCH",
                        "request_headers": dict(policy_headers),
                        "api_key_present": "X-API-KEY" in policy_headers,
                        "api_key_value": policy_headers.get("X-API-KEY", "NOT_FOUND"),
                        "content_type": policy_headers.get("Content-Type", "NOT_FOUND"),
                        "payload_structure": {
                            "website": webhook_payload.get("website"),
                            "scrapeRequestUuid": webhook_payload.get("scrapeRequestUuid"),
                            "status": webhook_payload.get("status"),
                            "org_id": webhook_payload.get("org_id"),
                            "org_id_type": type(webhook_payload.get("org_id")).__name__,
                            "policies_count": len(webhook_payload.get("policies", [])),
                            "createdDate": webhook_payload.get("createdDate")
                        },
                        "full_payload": webhook_payload  # Complete payload for debugging
                    }
                )

                # 🚀 FINAL REQUEST LOGGING - EXACTLY WHAT'S BEING SENT
                logger.info(
                    "🔥 POLICY PATCH REQUEST - FINAL TRANSMISSION",
                    {
                        "construction_step": "4_sending_request",
                        "timestamp": datetime.now().isoformat(),
                        "about_to_send": True,
                        "final_url": policy_webhook_url,
                        "final_method": "PATCH",
                        "final_headers": dict(policy_headers),
                        "final_payload": webhook_payload,
                        "final_timeout": timeout_seconds,
                        "curl_equivalent": f"curl -X PATCH '{policy_webhook_url}' " + 
                                          f"-H 'Content-Type: {policy_headers.get('Content-Type', 'application/json')}' " +
                                          f"-H 'X-API-KEY: {policy_headers.get('X-API-KEY', 'MISSING')}' " +
                                          f"-d '{json.dumps(webhook_payload)}'"
                    }
                )

                response = client.patch(policy_webhook_url, json=webhook_payload, headers=policy_headers)
                
                # 🚀 IMMEDIATE RESPONSE LOGGING
                logger.info(
                    "⚡ POLICY PATCH RESPONSE - IMMEDIATE RESULT",
                    {
                        "construction_step": "5_response_received",
                        "timestamp": datetime.now().isoformat(),
                        "response_status_code": response.status_code,
                        "response_headers": dict(response.headers),
                        "response_text": response.text,
                        "response_success": response.status_code == 200,
                        "response_size": len(response.text) if response.text else 0,
                        "request_url": policy_webhook_url
                    }
                )
        finally:
            # Restore original logging level
            httpx_logger.setLevel(original_level)

            # Log response details with success analysis
            success = response.status_code == 200
            logger.info(
                "📥 Policy PATCH response received",
                {
                    "status_code": response.status_code,
                    "success": success,
                    "response_text": response.text,
                    "response_headers": dict(response.headers),
                    "url": policy_webhook_url,
                    "payload_size_kb": payload_size_kb,
                    "optimization_effectiveness": "successful" if success and payload_size_kb <= 10 else "needs_improvement",
                    "webhook_stage": "post_send"
                }
            )

            if response.status_code == 200:
                logger.info("✅ Policy results webhook sent successfully")
                return True
            elif response.status_code == 401:
                logger.error(
                    "❌ Policy results webhook failed - Authentication error",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": policy_webhook_url,
                        "error": "Invalid API key - check BIZTEL_API_KEY in .env file",
                        "api_key_length": len(api_key) if api_key else 0
                    }
                )
                return False
            elif response.status_code == 500:
                logger.error(
                    "❌ Policy results webhook failed - Server error (possibly payload too large)",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": policy_webhook_url,
                        "payload_size_bytes": payload_size,
                        "payload_size_kb": round(payload_size / 1024, 2),
                        "suggestion": "Consider reducing screenshot size or removing large images"
                    }
                )
                return False
            else:
                logger.error(
                    "❌ Policy results webhook failed with non-200 status",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": policy_webhook_url,
                        "payload_size_bytes": payload_size
                    }
                )
                return False

    except Exception as e:
        logger.error(
            "❌ Critical error sending policy results webhook",
            {
                "scrape_request_ref_id": scrape_request_ref_id,
                "analysis_id": analysis_id,
                "url": policy_webhook_url if 'policy_webhook_url' in locals() else "unknown",
                "error": str(e),
                "error_type": type(e).__name__,
                "payload": webhook_payload if 'webhook_payload' in locals() else None
            }
        )

        # Always try to create and log default values even if webhook completely fails
        try:
            logger.info("🔄 Creating default policy webhook payload after complete failure")
            default_payload = {
                "website": website if website else "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "FAILED",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "policies": [
                    {"type": "RCE", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "PP", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "TC", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "SD", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"},
                    {"type": "CU", "url": "insufficient data", "imglink": "insufficient data", "text": "insufficient data"}
                ],
                "org_id": 1  # Integer default for Java server compatibility
            }
            logger.info("📋 Default policy payload created for failed webhook", {"payload": default_payload})
        except Exception as default_error:
            logger.error(f"Failed to create default policy payload after webhook error: {default_error}")

        return False


def send_risky_classification_webhook(
    scrape_request_ref_id: str,
    website: str,
    risky_result: dict,
    analysis_id: int,
    logger: ConsoleLogger
) -> bool:
    
    try:
        # Validate webhook configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)

        if not base_url:
            logger.error("BASE_URL not configured, cannot send risky classification webhook")
            return False

        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send risky classification webhook")
            return False

        # Prepare webhook URL and headers - RISKY CLASSIFICATION SPECIFIC
        risky_webhook_url = f"{base_url}/api/risky/results"
        risky_headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }

        # Log configuration details
        logger.info(
            "📤 Risky classification webhook configuration",
            {
                "base_url": base_url,
                "webhook_url": risky_webhook_url,
                "api_key_configured": bool(api_key),
                "api_key_length": len(api_key) if api_key else 0,
                "scrape_request_ref_id": scrape_request_ref_id,
                "analysis_id": analysis_id
            }
        )

        # Prepare webhook payload according to specification
        try:
            # Extract URLs from risky result
            urls_analyzed = []
            enumerated_urls = risky_result.get("enumerated_urls", [])
            
            url_results = risky_result.get("url_results", [])
            for url_result in url_results:
                if url_result.get("url"):
                    urls_analyzed.append(url_result["url"])

            # Determine risky status
            is_risky = risky_result.get("is_risky", False)
            risky_status = "yes" if is_risky else "no"

            # Extract categories
            risk_categories = risky_result.get("risk_categories", [])
            if isinstance(risk_categories, str):
                try:
                    risk_categories = json.loads(risk_categories)
                except json.JSONDecodeError:
                    risk_categories = []

            # Extract keywords and reasons from URL results
            keywords_found = []
            reasons_for_risky = []

            # First, try to get keywords and reasons from the main risky_result
            main_keywords = risky_result.get("keywords_found", [])
            main_reasons = risky_result.get("reason_for_risky", [])

            # Handle main keywords
            if isinstance(main_keywords, str) and main_keywords:
                try:
                    parsed_main_keywords = json.loads(main_keywords)
                    if isinstance(parsed_main_keywords, list):
                        keywords_found.extend(parsed_main_keywords)
                except json.JSONDecodeError:
                    pass
            elif isinstance(main_keywords, list):
                keywords_found.extend(main_keywords)

            # Handle main reasons
            if isinstance(main_reasons, str) and main_reasons:
                try:
                    parsed_main_reasons = json.loads(main_reasons)
                    if isinstance(parsed_main_reasons, list):
                        reasons_for_risky.extend(parsed_main_reasons)
                except json.JSONDecodeError:
                    pass
            elif isinstance(main_reasons, list):
                reasons_for_risky.extend(main_reasons)

            logger.info(
                "🔍 REASON_FOR_RISKY DEBUG - Main Data Extraction",
                {
                    "main_keywords_type": type(main_keywords).__name__,
                    "main_keywords_value": main_keywords,
                    "main_reasons_type": type(main_reasons).__name__,
                    "main_reasons_value": main_reasons,
                    "extracted_keywords_count": len(keywords_found),
                    "extracted_reasons_count": len(reasons_for_risky),
                    "extracted_keywords": keywords_found,
                    "extracted_reasons": reasons_for_risky
                }
            )

            for url_result in url_results:
                # Extract keywords directly from url_result
                url_keywords = url_result.get("keywords_found", [])
                if isinstance(url_keywords, list):
                    keywords_found.extend(url_keywords)
                elif isinstance(url_keywords, str) and url_keywords:
                    try:
                        parsed_keywords = json.loads(url_keywords)
                        if isinstance(parsed_keywords, list):
                            keywords_found.extend(parsed_keywords)
                    except json.JSONDecodeError:
                        pass

                # Extract reasons from url_result
                url_reasons = url_result.get("reason_for_risky", [])
                if isinstance(url_reasons, list):
                    reasons_for_risky.extend(url_reasons)
                elif isinstance(url_reasons, str) and url_reasons:
                    try:
                        parsed_reasons = json.loads(url_reasons)
                        if isinstance(parsed_reasons, list):
                            reasons_for_risky.extend(parsed_reasons)
                    except json.JSONDecodeError:
                        pass

                # Extract keywords and reasons from analysis details as fallback
                analysis_details = url_result.get("analysis_details", {})
                if isinstance(analysis_details, str):
                    try:
                        analysis_details = json.loads(analysis_details)
                    except json.JSONDecodeError:
                        analysis_details = {}

                # Add analysis text as potential reason if the URL is risky
                analysis_text = analysis_details.get("analysis", "")
                if analysis_text and url_result.get("is_risky", False):
                    reasons_for_risky.append(analysis_text)

                # Add keywords from analysis details if available
                analysis_keywords = analysis_details.get("keywords_found", [])
                if isinstance(analysis_keywords, list):
                    keywords_found.extend(analysis_keywords)

            # Remove duplicates
            keywords_found = list(set(keywords_found))
            reasons_for_risky = list(set(reasons_for_risky))

            logger.info(
                "🔍 REASON_FOR_RISKY DEBUG - After Deduplication",
                {
                    "final_keywords_count": len(keywords_found),
                    "final_reasons_count": len(reasons_for_risky),
                    "final_keywords": keywords_found,
                    "final_reasons": reasons_for_risky,
                    "url_results_count": len(url_results)
                }
            )

            # Get org_id from risky_result or default to 1
            org_id = risky_result.get("org_id", 1)
            if isinstance(org_id, str) and org_id.isdigit():
                org_id = int(org_id)
            elif not isinstance(org_id, int):
                org_id = 1

            # Convert URLs array to dictionary format with string integer keys
            urls_dict = {}
            urls_to_use = urls_analyzed if urls_analyzed else [website]
            for i, url in enumerate(urls_to_use, 1):
                urls_dict[str(i)] = url

            # Create webhook payload matching required format
            webhook_payload = {
                "website": website if website else "insufficient data",
                "scrapeRequestRefID": scrape_request_ref_id,
                "org_id": org_id,
                "urls": urls_dict,
                "risky": risky_status,
                "category": risk_categories if risk_categories else [],
                "keywords_found": keywords_found if keywords_found else [],
                "reason_for_risky": reasons_for_risky if reasons_for_risky else []
            }

            logger.info(
                "📋 Risky classification webhook payload prepared",
                {
                    "website": website,
                    "scrape_request_ref_id": scrape_request_ref_id,
                    "urls_count": len(webhook_payload["urls"]),
                    "urls_dict": webhook_payload["urls"],
                    "risky_status": webhook_payload["risky"],
                    "categories_count": len(webhook_payload["category"]),
                    "keywords_count": len(webhook_payload["keywords_found"]),
                    "reasons_count": len(webhook_payload["reason_for_risky"]),
                    "payload_size_bytes": len(str(webhook_payload))
                }
            )

        except Exception as payload_error:
            logger.error(f"Error creating risky classification webhook payload: {payload_error}")
            # Create default payload with dictionary format for URLs
            default_url = website if website else "insufficient data"
            webhook_payload = {
                "website": website if website else "insufficient data",
                "scrapeRequestRefID": scrape_request_ref_id,
                "org_id": 1,
                "urls": {"1": default_url},
                "risky": "no",
                "category": [],
                "keywords_found": [],
                "reason_for_risky": ["Analysis failed - insufficient data"]
            }

        # Send webhook request
        timeout_seconds = 30.0

        logger.info(
            "🚀 About to send Risky Classification PATCH request",
            {
                "url": risky_webhook_url,
                "method": "PATCH",
                "headers_being_sent": risky_headers,
                "timeout_seconds": timeout_seconds,
                "payload": webhook_payload
            }
        )

        try:
            with httpx.Client(timeout=timeout_seconds) as client:
                logger.info(
                    "📡 RISKY CLASSIFICATION WEBHOOK REQUEST TRACKING",
                    {
                        "client_timeout": timeout_seconds,
                        "request_url": risky_webhook_url,
                        "request_method": "PATCH",
                        "request_headers": dict(risky_headers),
                        "api_key_present": "X-API-KEY" in risky_headers,
                        "payload_structure": {
                            "website": webhook_payload.get("website"),
                            "url_count": len(webhook_payload.get("urls", {})),
                            "urls_dict": webhook_payload.get("urls", {}),
                            "risky": webhook_payload.get("risky"),
                            "category_count": len(webhook_payload.get("category", [])),
                            "keywords_count": len(webhook_payload.get("keywords_found", [])),
                            "reasons_count": len(webhook_payload.get("reason_for_risky", []))
                        },
                        "full_payload": webhook_payload
                    }
                )
                
                # 🚀 FINAL RISKY REQUEST LOGGING - EXACTLY WHAT'S BEING SENT
                logger.info(
                    "🔥 RISKY PATCH REQUEST - FINAL TRANSMISSION",
                    {
                        "construction_step": "2_sending_risky_request",
                        "timestamp": datetime.now().isoformat(),
                        "about_to_send": True,
                        "final_url": risky_webhook_url,
                        "final_method": "PATCH",
                        "final_headers": dict(risky_headers),
                        "final_payload": webhook_payload,
                        "final_timeout": 30.0,
                        "curl_equivalent": f"curl -X PATCH '{risky_webhook_url}' " + 
                                          f"-H 'Content-Type: {risky_headers.get('Content-Type', 'application/json')}' " +
                                          f"-H 'X-API-KEY: {risky_headers.get('X-API-KEY', 'MISSING')}' " +
                                          f"-d '{json.dumps(webhook_payload)}'"
                    }
                )
                
                response = client.patch(risky_webhook_url, json=webhook_payload, headers=risky_headers)
                
                # 🚀 IMMEDIATE RISKY RESPONSE LOGGING
                logger.info(
                    "⚡ RISKY PATCH RESPONSE - IMMEDIATE RESULT",
                    {
                        "construction_step": "3_risky_response_received",
                        "timestamp": datetime.now().isoformat(),
                        "response_status_code": response.status_code,
                        "response_headers": dict(response.headers),
                        "response_text": response.text,
                        "response_success": response.status_code == 200,
                        "response_size": len(response.text) if response.text else 0,
                        "request_url": risky_webhook_url
                    }
                )

                # Log response details
                logger.info(
                    "📨 Risky Classification PATCH response received",
                    {
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_headers": dict(response.headers),
                        "url": risky_webhook_url,
                        "success": 200 <= response.status_code < 300
                    }
                )

                if 200 <= response.status_code < 300:
                    logger.info(
                        "✅ Risky classification webhook sent successfully",
                        {
                            "scrape_request_ref_id": scrape_request_ref_id,
                            "analysis_id": analysis_id,
                            "status_code": response.status_code,
                            "website": website
                        }
                    )
                    return True
                else:
                    logger.error(
                        "❌ Risky classification webhook failed with non-2xx status",
                        {
                            "scrape_request_ref_id": scrape_request_ref_id,
                            "analysis_id": analysis_id,
                            "status_code": response.status_code,
                            "response_text": response.text
                        }
                    )
                    return False

        except httpx.TimeoutException:
            logger.error(
                "⏰ Risky classification webhook timeout",
                {
                    "scrape_request_ref_id": scrape_request_ref_id,
                    "analysis_id": analysis_id,
                    "timeout_seconds": timeout_seconds,
                    "url": risky_webhook_url
                }
            )
            return False

        except Exception as request_error:
            logger.error(
                "❌ Risky classification webhook request failed",
                {
                    "scrape_request_ref_id": scrape_request_ref_id,
                    "analysis_id": analysis_id,
                    "error": str(request_error),
                    "error_type": type(request_error).__name__,
                    "url": risky_webhook_url
                }
            )
            return False

    except Exception as e:
        logger.error(
            "❌ Critical error sending risky classification webhook",
            {
                "scrape_request_ref_id": scrape_request_ref_id,
                "analysis_id": analysis_id,
                "url": risky_webhook_url if 'risky_webhook_url' in locals() else "unknown",
                "error": str(e),
                "error_type": type(e).__name__,
                "payload": webhook_payload if 'webhook_payload' in locals() else None
            }
        )
        return False