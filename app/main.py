from fastapi import <PERSON><PERSON><PERSON>, Request, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from celery import Celery
from app.routers import mcc_analysis, website_urls, risky_classification, policy_analysis
from app.database import init_db
from Extractor.routers.entity_extraction_router import router as entity_router
from Extractor.database_setup import create_entity_extractor_tables
import asyncio
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize the FastAPI app
app = FastAPI(title="FastAPI Service", version="1.0")

# Configure CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["34.47.244.221"],  # For production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database
@app.on_event("startup")
def on_startup():
    logger.info("Initializing application")
    try:
        init_db()
        logger.info("Database initialized successfully")
        
        # Initialize Entity Extractor tables
        create_entity_extractor_tables()
        logger.info("Entity Extractor tables initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        # Don't raise here - just log the error so we can still start the app
        # and serve the health check endpoints, etc.

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Application shutting down")
    pass

@app.middleware("http")
async def log_request_body(request: Request, call_next):
    body = await request.body()
    logger.debug(f"Raw Request Body: {body.decode('utf-8')}")
    request = Request(request.scope, asyncio.StreamReader())
    request._receive = lambda: {"type": "http.request", "body": body}
    return await call_next(request)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Service is running"}


