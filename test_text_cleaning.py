#!/usr/bin/env python3
"""
Test script to demonstrate the text cleaning functionality for OpenAI fallback.
This script shows how the new cleaning function removes navigation menus and other
non-content elements from HTML content.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Extractor.services.entity_extractor_orchestrator import EntityExtractorOrchestrator
from Extractor.utils.logger import EntityExtractorLogger

def test_text_cleaning():
    """Test the text cleaning functionality with the example HTML content."""

    # Initialize the orchestrator (we just need it for the cleaning method)
    logger = EntityExtractorLogger("test_cleaning", "test_request")
    orchestrator = EntityExtractorOrchestrator()
    orchestrator.logger = logger
    
    # Example HTML content with navigation menus (like the one you provided)
    raw_html_content = """
    Privacy Policy - grmelitewear Skip to content HomeShop Menu Toggle Kurtis Menu Toggle Normal Kurtis Feeding Kurtis Casual Wear / Outdoor wear Menu Toggle Front button Midi Frock Non-feeding Normal Sleeve Non-feeding Puff Sleeve Non-feeding Full Sleeve Non feeding – Normal Sleeve (Calf length ) Non feeding – Puff Sleeve (Calf length ) Maternity wear Menu Toggle Feeding Normal Sleeve Feeding Full Sleeve Feeding – Normal Sleeve (Calf length ) Maternity wear puff Menu Toggle Feeding Puff Sleeve Feeding – Puff Sleeve (Calf length ) Women's CO – ORD set Menu Toggle Feeding Pyjamas – Co-ord set Women's Night suit Knee Pant DevotionalKids Menu Toggle Tights for School girls – Cycling Shorts Unisex – Co ord Set Jumpsuits Kids Frock Muslin Products Menu Toggle Muslin Front Button Frock Muslin Jabla Muslin Towel Muslin Swaddle Nappy Rompers Co-ord set Cotton JablaKids & Newborn Products Menu Toggle Shoes & Socks Padded Underwear & Nappy Kids bed DrysheetWomen's Product Menu Toggle Maternity Sanitary Pad Baby Carrier Bag Mom's Bag Women's Inner Wear Shawl /Stole Jeggins Palazzo pantsAbout Search for: Search Search Main Menu HomeShop Menu Toggle Kurtis Menu Toggle Normal Kurtis Feeding Kurtis Casual Wear / Outdoor wear Menu Toggle Front button Midi Frock Non-feeding Normal Sleeve Non-feeding Puff Sleeve Non-feeding Full Sleeve Non feeding – Normal Sleeve (Calf length ) Non feeding – Puff Sleeve (Calf length ) Maternity wear Menu Toggle Feeding Normal Sleeve Feeding Full Sleeve Feeding – Normal Sleeve (Calf length ) Maternity wear puff Menu Toggle Feeding Puff Sleeve Feeding – Puff Sleeve (Calf length ) Women's CO – ORD set Menu Toggle Feeding Pyjamas – Co-ord set Women's Night suit Knee Pant DevotionalKids Menu Toggle Tights for School girls – Cycling Shorts Unisex – Co ord Set Jumpsuits Kids Frock Muslin Products Menu Toggle Muslin Front Button Frock Muslin Jabla Muslin Towel Muslin Swaddle Nappy Rompers Co-ord set Cotton JablaKids & Newborn Products Menu Toggle Shoes & Socks Padded Underwear & Nappy Kids bed DrysheetWomen's Product Menu Toggle Maternity Sanitary Pad Baby Carrier Bag Mom's Bag Women's Inner Wear Shawl /Stole Jeggins Palazzo pantsAboutPrivacy Statement PolicyPrivacy StatementA COMMITMENT TO SAFEGUARD CUSTOMER DATAGrmelitewear provides importance to your data privacy by securing personal information. This privacy policy provides information on why do we collect the data, how do we protect your personal data and how the collected information are used.WHY DO WE COLLECT USER INFORMATION?Grmelitewear collects the user data while applying for the job opening and when you are requesting a quote of our product or services. We may collect the following customer data and use the data to reach you directly for processing the job application and for direct marketing of our product offers, discounts, and services.HOW DO WE USE THE INFORMATION WE COLLECT?We use the collected information for sending newsletters, offers, product promotions, and interview call letters. Your email ids will be hidden from other customers while sending promotional emails Grmelitewear and its subsidiaries will not release or exploit the user information in any manner.PROTECTION OF CUSTOMER DATAGrmelitewear stores the customer information in the secured database which will not be disclosed to any third party agencies or individual users without the prior permission from the customers. zoy care may share the client data with its subsidiaries for introducing product offers, discounts and promote the product or services.CUSTOMER COMMUNICATIONWe reach the customers with promotional emails or information emails which you can opt out by unsubscribing our mailer. If you have any queries or clarifications, you can contact our customer support.POLICY CHANGEThe above policy is subject to change and the changes we made will be updated periodically in the website; all user registrations made in Grmelitewear to the privacy policy. <NAME_EMAIL> Nagar, Kadampadi (PO), Sulur Aero, Coimbatore – 641401Quick LinksPrivacy PolicyShipping & Delivery PolicyTerm & ConditionCancellation & Refund PolicyCopyright © 2025 grmelitewear | Powered by grmelitewear  3-5 Days Delivery within Tamil Nādu Purchase above ₹ 1000  + Shipping charges                                                                 7-10 Days Delivery For Other States
    """
    
    print("=" * 80)
    print("ORIGINAL CONTENT (first 500 characters):")
    print("=" * 80)
    print(raw_html_content[:500] + "...")
    print(f"\nOriginal length: {len(raw_html_content)} characters")
    
    print("\n" + "=" * 80)
    print("CLEANING THE CONTENT...")
    print("=" * 80)
    
    # Clean the content
    cleaned_content = orchestrator._clean_policy_text(raw_html_content)
    
    print("CLEANED CONTENT:")
    print("=" * 80)
    print(cleaned_content)
    print(f"\nCleaned length: {len(cleaned_content)} characters")
    print(f"Reduction: {len(raw_html_content) - len(cleaned_content)} characters ({((len(raw_html_content) - len(cleaned_content)) / len(raw_html_content) * 100):.1f}%)")
    
    print("\n" + "=" * 80)
    print("ANALYSIS:")
    print("=" * 80)
    print("✓ Removed repeated navigation menus")
    print("✓ Extracted main privacy policy content")
    print("✓ Preserved important policy information")
    print("✓ Significantly reduced content length while maintaining quality")

if __name__ == "__main__":
    test_text_cleaning()
