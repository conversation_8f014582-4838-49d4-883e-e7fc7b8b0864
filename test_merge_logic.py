#!/usr/bin/env python3
"""
Test script to verify the improved merge logic for entity extraction.
This script tests that OpenAI values are used when Gemini returns null values.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Extractor.services.entity_extractor_orchestrator import EntityExtractorOrchestrator
from Extractor.utils.logger import EntityExtractor<PERSON>ogger

def test_merge_logic():
    """Test the merge logic with various scenarios."""
    
    # Initialize the orchestrator
    logger = EntityExtractorLogger("test_merge", "test_request")
    orchestrator = EntityExtractorOrchestrator()
    orchestrator.logger = logger
    
    print("=" * 80)
    print("TESTING MERGE LOGIC")
    print("=" * 80)
    
    # Test Case 1: Gemini has null values, OpenAI has actual values
    print("\nTest Case 1: Gemini null, OpenAI has values")
    print("-" * 50)
    
    gemini_result = {
        "legal_name": None,
        "business_email": None,
        "support_email": None,
        "business_contact_numbers": None,
        "business_location": None
    }
    
    openai_results = [{
        "legal_name": "Grmelitewear",
        "business_email": "<EMAIL>",
        "support_email": "<EMAIL>",
        "business_contact_numbers": "8754652625",
        "business_location": "Rajalingam Nagar, Kadampadi (PO), Sulur Aero, Coimbatore – 641401"
    }]
    
    merged = orchestrator._merge_extraction_results(gemini_result, openai_results)
    
    print("Gemini result:", gemini_result)
    print("OpenAI result:", openai_results[0])
    print("Merged result:", merged)
    
    # Verify that OpenAI values are used
    assert merged.get("legal_name") == "Grmelitewear", f"Expected 'Grmelitewear', got {merged.get('legal_name')}"
    assert merged.get("business_email") == "<EMAIL>", f"Expected '<EMAIL>', got {merged.get('business_email')}"
    print("✓ Test Case 1 PASSED: OpenAI values used when Gemini has nulls")
    
    # Test Case 2: Gemini has values, OpenAI has different values
    print("\nTest Case 2: Both have values - single fields prioritize Gemini, list fields combine")
    print("-" * 50)

    gemini_result = {
        "legal_name": "Grmelitewear Official",
        "business_email": "<EMAIL>"
    }

    openai_results = [{
        "legal_name": "Grmelitewear",
        "business_email": "<EMAIL>"
    }]

    merged = orchestrator._merge_extraction_results(gemini_result, openai_results)

    print("Gemini result:", gemini_result)
    print("OpenAI result:", openai_results[0])
    print("Merged result:", merged)

    # Verify that single fields prioritize Gemini, list fields combine
    assert merged.get("legal_name") == "Grmelitewear Official", f"Expected 'Grmelitewear Official', got {merged.get('legal_name')}"
    # business_email is a list field, so it should combine both values
    expected_email = "<EMAIL>, <EMAIL>"
    assert merged.get("business_email") == expected_email, f"Expected '{expected_email}', got {merged.get('business_email')}"
    print("✓ Test Case 2 PASSED: Single fields prioritize Gemini, list fields combine values")
    
    # Test Case 3: Mixed scenario - some fields from Gemini, some from OpenAI
    print("\nTest Case 3: Mixed scenario")
    print("-" * 50)
    
    gemini_result = {
        "legal_name": "Grmelitewear Official",
        "business_email": None,
        "support_email": "<EMAIL>"
    }
    
    openai_results = [{
        "legal_name": "Grmelitewear",
        "business_email": "<EMAIL>",
        "support_email": None,
        "business_contact_numbers": "8754652625"
    }]
    
    merged = orchestrator._merge_extraction_results(gemini_result, openai_results)
    
    print("Gemini result:", gemini_result)
    print("OpenAI result:", openai_results[0])
    print("Merged result:", merged)
    
    # Verify mixed results
    assert merged.get("legal_name") == "Grmelitewear Official", "Should use Gemini legal_name"
    assert merged.get("business_email") == "<EMAIL>", "Should use OpenAI business_email (Gemini was null)"
    assert merged.get("support_email") == "<EMAIL>", "Should use Gemini support_email"
    assert merged.get("business_contact_numbers") == "8754652625", "Should use OpenAI business_contact_numbers"
    print("✓ Test Case 3 PASSED: Mixed scenario works correctly")
    
    print("\n" + "=" * 80)
    print("ALL TESTS PASSED!")
    print("The merge logic now correctly prioritizes non-null values.")
    print("=" * 80)

if __name__ == "__main__":
    test_merge_logic()
