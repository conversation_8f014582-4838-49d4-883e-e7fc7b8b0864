"""
Entity Extraction Service

Main service for extracting business entities from website URLs
"""

import json
import asyncio
from typing import Dict, List, Optional, Any

from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_legacy
from app.gpt_models.chatgpt_utils import call_gpt4o_mini
from Extractor.utils.logger import EntityExtractorLogger
from Extractor.prompts.entity_extraction_prompts import EntityExtractionPrompts
from Extractor.services.url_data_retrieval import UrlDataRetrievalService
from Extractor.utils.api_response_logger import api_logger


class EntityExtractionService:
    """
    Service for extracting business entities from website pages
    """
    
    def __init__(self, scrape_request_ref_id: str, website_url: str = "", org_id: str = "default", analysis_id: int = None):
        self.scrape_request_ref_id = scrape_request_ref_id
        self.website_url = website_url
        self.org_id = org_id
        self.analysis_id = analysis_id
        from Extractor.utils.logger import EntityExtractorLogger
        self.logger = EntityExtractorLogger(analysis_id=analysis_id, scrape_request_id=scrape_request_ref_id)
        from Extractor.services.url_data_retrieval import UrlDataRetrievalService
        self.url_retrieval_service = UrlDataRetrievalService(scrape_request_ref_id, org_id)
    
    def extract_entities(self, filtered_policy_urls: dict, reachable_urls: list, unreachable_urls: list) -> dict:
        """
        Extract entities using Gemini for reachable URLs (single call), OpenAI for unreachable URLs (one by one).
        """
        gemini_result = {}
        openai_results = []
        # NOTE: Gemini extraction is now handled by the orchestrator to avoid duplicate calls
        # This service only handles OpenAI fallback for unreachable URLs
        gemini_result = {}
        # OpenAI extraction (following diagram workflow)
        if unreachable_urls:
            from Extractor.prompts.entity_extraction_prompts import EntityExtractionPrompts
            from app.gpt_models.chatgpt_utils import call_gpt4o_mini
            for k, v in filtered_policy_urls.items():
                url = v.get('url') or v.get('original_url')
                if url in unreachable_urls:
                    # Only use text from policy_analysis_new_gemini table
                    text_content = None
                    # Try to get text from policy_analysis_new_gemini table only
                    text_content = self.url_retrieval_service._get_text_from_policy_table(url)
                    if text_content:
                        
                        # Apply text splitting logic as per diagram
                        processed_text = self._process_text_for_openai(text_content)
                        prompt = EntityExtractionPrompts.get_entity_extraction_text_prompt(url, processed_text)
                        prompt += "\n\nIMPORTANT: Only use the provided text (from policy analysis tables). Extract shipment/international shipping yes/no as well."

                        # Prepare messages for OpenAI
                        messages = [{"role": "user", "content": prompt}]
                        openai_response_obj = call_gpt4o_mini(messages, name="entity_extraction")
                        
                        # Log OpenAI API response
                        api_logger.log_openai_response(
                            messages=messages,
                            response=openai_response_obj,
                            name="entity_extraction",
                            request_id=self.scrape_request_ref_id,
                            context={"service": "entity_extraction", "url": url}
                        )
                        openai_response = openai_response_obj.choices[0].message.content if openai_response_obj else None

                        import json
                        if openai_response:
                            try:
                                openai_result = json.loads(openai_response)
                            except Exception as e:
                                openai_result = {"error": f"OpenAI response parse error: {str(e)}", "raw": openai_response}
                            openai_results.append(openai_result)
                        else:
                            openai_results.append({"error": "No response from OpenAI", "url": url})
        # Merge results
        merged_result = {}
        if gemini_result:
            merged_result.update(gemini_result)
        for res in openai_results:
            merged_result.update(res)
        return merged_result

    def _process_text_for_openai(self, text_content: str) -> str:
        """
        Process text content according to diagram specifications:
        - Split text by spaces
        - If len(words) < 80k: pass as-is
        - If len(words) > 80k: take first 40k + last 40k words
        - Convert back to text
        """
        try:
            # Split text into words by space
            words = text_content.split()
            word_count = len(words)

            self.logger.info(f"Processing text with {word_count} words for OpenAI")

            if word_count <= 80000:
                # Text is within limit, pass as-is
                self.logger.info("Text within 80k word limit, using full text")
                return text_content
            else:
                # Text exceeds limit, take first 40k + last 40k words
                self.logger.info("Text exceeds 80k words, taking first 40k + last 40k words")
                first_40k = words[:40000]
                last_40k = words[-40000:]

                # Combine and convert back to text
                processed_words = first_40k + last_40k
                processed_text = ' '.join(processed_words)

                self.logger.info(f"Processed text: {len(processed_words)} words total")
                return processed_text

        except Exception as e:
            self.logger.error(f"Error processing text for OpenAI: {str(e)}")
            # Fallback: return first 8000 characters
            return text_content[:8000]

    # def _clean_policy_text(self, html_content: str) -> str:
    #     """
    #     Clean HTML content to extract only the main policy text, removing navigation,
    #     menus, footers, and other non-content elements.

    #     Args:
    #         html_content: Raw HTML content or text content

    #     Returns:
    #         Cleaned text content with only the main policy information
    #     """
    #     try:
    #         # If the content doesn't look like HTML, return as-is
    #         if not html_content or '<' not in html_content:
    #             return html_content

    #         from bs4 import BeautifulSoup
    #         import re

    #         # Parse the HTML content
    #         soup = BeautifulSoup(html_content, 'html.parser')

    #         # Remove script, style, nav, header, footer, and menu elements
    #         for element in soup(['script', 'style', 'nav', 'header', 'footer', 'menu']):
    #             element.decompose()

    #         # Remove elements with common navigation/menu class names and IDs
    #         nav_selectors = [
    #             '[class*="nav"]', '[class*="menu"]', '[class*="header"]', '[class*="footer"]',
    #             '[class*="sidebar"]', '[class*="breadcrumb"]', '[id*="nav"]', '[id*="menu"]',
    #             '[id*="header"]', '[id*="footer"]', '[id*="sidebar"]'
    #         ]

    #         for selector in nav_selectors:
    #             for element in soup.select(selector):
    #                 element.decompose()

    #         # Try to find the main content area
    #         main_content = None

    #         # Look for common main content selectors
    #         main_selectors = [
    #             'main', '[role="main"]', '.main-content', '#main-content',
    #             '.content', '#content', '.policy-content', '.privacy-policy',
    #             '.terms-conditions', 'article', '.article-content'
    #         ]

    #         for selector in main_selectors:
    #             main_content = soup.select_one(selector)
    #             if main_content:
    #                 break

    #         # If no main content found, use the body or the whole soup
    #         if not main_content:
    #             main_content = soup.find('body') or soup

    #         # Extract text from the main content
    #         text = main_content.get_text(separator=' ', strip=True)

    #         # Clean up the text
    #         # Remove excessive whitespace
    #         text = re.sub(r'\s+', ' ', text)

    #         # Remove repeated navigation patterns (common in the example you provided)
    #         # Remove repeated menu items that appear multiple times
    #         lines = text.split('\n')
    #         seen_lines = set()
    #         unique_lines = []

    #         for line in lines:
    #             line = line.strip()
    #             if line and line not in seen_lines and len(line) > 10:  # Only keep substantial unique lines
    #                 seen_lines.add(line)
    #                 unique_lines.append(line)

    #         cleaned_text = '\n'.join(unique_lines)

    #         # If the cleaned text is too short, fall back to basic cleaning
    #         if len(cleaned_text.strip()) < 100:
    #             # Basic fallback: just remove scripts and styles, get all text
    #             soup = BeautifulSoup(html_content, 'html.parser')
    #             for element in soup(['script', 'style']):
    #                 element.decompose()
    #             cleaned_text = soup.get_text(separator=' ', strip=True)
    #             cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

    #         self.logger.info(f"Text cleaning: Original length: {len(html_content)}, Cleaned length: {len(cleaned_text)}")
    #         return cleaned_text.strip()

    #     except Exception as e:
    #         self.logger.warning(f"Error cleaning policy text: {str(e)}")
    #         # Fallback: basic text cleaning
    #         try:
    #             import re
    #             # Remove HTML tags if present
    #             text = re.sub(r'<[^>]+>', ' ', html_content)
    #             # Clean up whitespace
    #             text = re.sub(r'\s+', ' ', text)
    #             return text.strip()
    #         except:
    #             return html_content

    def _create_empty_result(self, error_message: str) -> Dict[str, Any]:
        """
        Create an empty result with error message
        
        Args:
            error_message: Error description
            
        Returns:
            Empty result dictionary
        """
        return {
            "legal_name": None,
            "business_email": None,
            "support_email": None,
            "business_contact_numbers": None,
            "extraction_method": "failed",
            "error": error_message
        }

    # Database Storage Methods
    
    def _create_analysis_record(self) -> int:
        """
        Create initial analysis record in entity_extraction_analysis table with duplicate check
        
        Returns:
            Analysis ID
        """
        try:
            from app.models.db_models import EntityExtractionAnalysis
            from app.database import get_session
            from app.models.db_models import get_current_time
            from sqlmodel import select

            with next(get_session()) as session:
                # Check for existing record first
                existing_check = select(EntityExtractionAnalysis).where(
                    EntityExtractionAnalysis.scrape_request_ref_id == self.scrape_request_ref_id,
                    EntityExtractionAnalysis.org_id == self.org_id
                ).order_by(EntityExtractionAnalysis.id.desc())
                
                existing = session.exec(existing_check).first()
                if existing:
                    self.logger.warning(f"Found existing analysis record with ID: {existing.id}")
                    return existing.id

                # Create new record only if none exists
                analysis = EntityExtractionAnalysis(
                    scrape_request_ref_id=self.scrape_request_ref_id,
                    org_id=self.org_id,
                    analysis_status="PROCESSING",
                    started_at=get_current_time()
                )
                
                session.add(analysis)
                session.commit()
                session.refresh(analysis)
                
                self.logger.info(f"Created analysis record with ID: {analysis.id}")
                return analysis.id

        except Exception as e:
            self.logger.error("Error creating analysis record", error=e)
            return 0

    def _update_analysis_status(self, status: str, error_message: str = None):
        """
        Update analysis status and error message
        
        Args:
            status: New status (PROCESSING, COMPLETED, FAILED)
            error_message: Optional error message
        """
        try:
            if not self.analysis_id:
                return

            from app.models.db_models import EntityExtractionAnalysis
            from app.database import get_session
            from app.models.db_models import get_current_time
            from sqlmodel import select

            with next(get_session()) as session:
                query = select(EntityExtractionAnalysis).where(
                    EntityExtractionAnalysis.id == self.analysis_id
                )
                analysis = session.exec(query).first()
                
                if analysis:
                    analysis.analysis_status = status
                    if error_message:
                        analysis.error_message = error_message
                    
                    if status == "COMPLETED":
                        analysis.completed_at = get_current_time()
                    elif status == "FAILED":
                        analysis.failed_at = get_current_time()
                    
                    session.add(analysis)
                    session.commit()

        except Exception as e:
            self.logger.error("Error updating analysis status", error=e)

    def _update_analysis_url_counts(self, total_urls: int, reachable_count: int, unreachable_count: int):
        """
        Update URL counts in analysis record
        
        Args:
            total_urls: Total number of URLs processed
            reachable_count: Number of reachable URLs
            unreachable_count: Number of unreachable URLs
        """
        try:
            if not self.analysis_id:
                return

            from app.models.db_models import EntityExtractionAnalysis
            from app.database import get_session
            from sqlmodel import select

            with next(get_session()) as session:
                query = select(EntityExtractionAnalysis).where(
                    EntityExtractionAnalysis.id == self.analysis_id
                )
                analysis = session.exec(query).first()
                
                if analysis:
                    analysis.total_urls_processed = total_urls
                    # Note: reachable_urls_count and unreachable_urls_count fields don't exist in DB schema
                    # URL counts are tracked in the urls_reachable_by_gemini and urls_not_reachable_by_gemini JSON fields

                    session.add(analysis)
                    session.commit()

        except Exception as e:
            self.logger.error("Error updating URL counts", error=e)

    def _store_url_analysis_records(self, policy_urls: Dict[str, Dict[str, Any]], reachable_urls: List[str], unreachable_urls: List[str]):
        """
        Store individual URL analysis records

        Args:
            policy_urls: Dictionary of policy URLs with their data
            reachable_urls: List of reachable URLs
            unreachable_urls: List of unreachable URLs
        """
        try:
            if not self.analysis_id:
                return

            from app.models.db_models import EntityExtractionUrlAnalysis
            from app.database import get_session

            with next(get_session()) as session:
                for policy_type, url_data in policy_urls.items():
                    # Extract the actual URL from the URL data dictionary
                    url = url_data.get('url') or url_data.get('original_url')
                    if not url:
                        continue

                    # Get text content length
                    text_content = self.url_retrieval_service.get_extracted_text_for_url(url)
                    text_length = len(text_content) if text_content else 0
                    
                    # Determine source table and classification
                    url_source_table = "policy_analysis_new_gemini"  # Default
                    url_classification = policy_type
                    
                    url_analysis = EntityExtractionUrlAnalysis(
                        analysis_id=self.analysis_id,
                        entity_analysis_id=self.analysis_id,  # Set both IDs for compatibility
                        scrape_request_ref_id=self.scrape_request_ref_id,
                        url=url,
                        url_classification=url_classification,
                        url_source_table=url_source_table,
                        is_reachable_by_gemini=(url in reachable_urls),  # FIXED: Use correct field name
                        extracted_text_length=text_length,
                        processing_status="PENDING",
                        org_id=self.org_id
                    )
                    
                    session.add(url_analysis)
                
                session.commit()
                self.logger.info(f"Stored {len(policy_urls)} URL analysis records")

        except Exception as e:
            self.logger.error("Error storing URL analysis records", error=e)

    def _store_extraction_results(self, result: Dict[str, Any], policy_urls: Dict[str, str], 
                                reachable_urls: List[str], unreachable_urls: List[str]):
        """
        Store final extraction results in database
        
        Args:
            result: Extraction result dictionary
            policy_urls: Dictionary of policy URLs
            reachable_urls: List of reachable URLs
            unreachable_urls: List of unreachable URLs
        """
        try:
            if not self.analysis_id:
                return

            from app.models.db_models import EntityExtractionAnalysis
            from app.database import get_session
            from app.models.db_models import get_current_time
            from sqlmodel import select
            import json

            with next(get_session()) as session:
                query = select(EntityExtractionAnalysis).where(
                    EntityExtractionAnalysis.id == self.analysis_id
                )
                analysis = session.exec(query).first()
                
                if analysis:
                    # Store extracted entities
                    analysis.legal_name = result.get("legal_name")
                    analysis.business_email = result.get("business_email")
                    analysis.support_email = result.get("support_email")
                    analysis.business_contact_numbers = result.get("business_contact_numbers")
                    
                    # Store extraction method
                    analysis.extraction_method = result.get("extraction_method", "unknown")

                    # Note: extraction_source fields are not stored in database schema
                    # but are available in the result for API responses
                    
                    # Store URL lists as JSON - These fields will be available after running init_mysql_database.py
                    analysis.all_urls_found = json.dumps(list(policy_urls.values()))
                    analysis.reachable_urls = json.dumps(reachable_urls)
                    analysis.unreachable_urls = json.dumps(unreachable_urls)
                    analysis.policy_urls_matched = json.dumps(policy_urls)

                    # Also store in the existing fields for backward compatibility
                    analysis.urls_reachable_by_gemini = json.dumps(reachable_urls)
                    analysis.urls_not_reachable_by_gemini = json.dumps(unreachable_urls)
                    
                    # Store processing details
                    processing_details = {
                        "extraction_method": result.get("extraction_method"),
                        "total_urls": len(policy_urls),
                        "reachable_count": len(reachable_urls),
                        "unreachable_count": len(unreachable_urls),
                        "entities_extracted": sum(1 for field in ["legal_name", "business_email", "support_email", "business_contact_numbers", "business_location"] 
                                                if result.get(field))
                    }
                    analysis.processing_details = json.dumps(processing_details)
                    
                    # Update status
                    if result.get("error"):
                        analysis.analysis_status = "FAILED"
                        analysis.error_message = result.get("error")
                        analysis.failed_at = get_current_time()
                    else:
                        analysis.analysis_status = "COMPLETED"
                        analysis.completed_at = get_current_time()
                    
                    session.add(analysis)
                    session.commit()
                    
                    self.logger.info("Stored extraction results in database", {
                        "analysis_id": self.analysis_id,
                        "status": analysis.analysis_status,
                        "entities_found": sum(1 for field in ["legal_name", "business_email", "support_email", "business_contact_numbers", "business_location"] 
                                            if result.get(field))
                    })

        except Exception as e:
            self.logger.error("Error storing extraction results", error=e)

