"""
Entity Extraction API Router

FastAPI router for entity extraction endpoints
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from sqlmodel import Session, select
from typing import List, Dict, Any

from app.database import get_session
from Extractor.models.request_models import EntityExtractionRequest, EntityExtractionResponse, SimpleEntityExtractionRequest
from app.models.db_models import EntityExtractionAnalysis
from Extractor.services.entity_extractor_orchestrator import EntityExtractorOrchestrator
from Extractor.services.url_data_retrieval import UrlDataRetrievalService
from Extractor.utils.logger import EntityExtractorLogger

router = APIRouter(prefix="/entity-extraction", tags=["Entity Extraction"])


@router.post("/analyze", response_model=EntityExtractionResponse)
async def analyze_entity_extraction(
    request: EntityExtractionRequest,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session)
):
    """
    Start entity extraction analysis for a website (simplified, only main flow)
    """
    orchestrator = EntityExtractorOrchestrator()
    return orchestrator.process_entity_extraction(request)


@router.post("/analyze-simple", response_model=EntityExtractionResponse)
async def analyze_entity_extraction_simple(
    request: SimpleEntityExtractionRequest,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session)
):
    """
    Simplified entity extraction - just provide website URL
    """
    scrape_request_ref_id = UrlDataRetrievalService.find_scrape_request_ref_id_by_website(
        request.website_url,
        request.org_id
    )
    if not scrape_request_ref_id:
        raise HTTPException(
            status_code=404,
            detail=f"No classification data found for website: {request.website_url}. Please ensure the website has been scraped and classified first."
        )
    full_request = EntityExtractionRequest(
        scrape_request_ref_id=scrape_request_ref_id,
        website_url=request.website_url,
        org_id=request.org_id,
        force_reprocess=request.force_reprocess,
        use_openai_fallback=request.use_openai_fallback
    )
    orchestrator = EntityExtractorOrchestrator()
    return orchestrator.process_entity_extraction(full_request)
