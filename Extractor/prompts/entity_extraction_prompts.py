"""
Entity Extraction Prompts - Main Module

This module imports prompts from specialized files for different AI models
"""

from .gemini_entity_prompts import GeminiEntityPrompts
from .openai_entity_prompts import OpenAIEntityPrompts


class EntityExtractionPrompts:
    """
    Main prompts class that delegates to model-specific prompt classes
    """

    @staticmethod
    def get_entity_extraction_prompt(urls_dict: dict) -> str:
        """
        Generate prompt for extracting entities from multiple URLs (for Gemini)

        Args:
            urls_dict: Dictionary of policy type to URL mapping
        """
        return GeminiEntityPrompts.get_entity_extraction_prompt(urls_dict)

    @staticmethod
    def get_entity_extraction_text_prompt(url: str, text_content: str, url_type: str = None) -> str:
        """
        Generate prompt for extracting entities from text content (for OpenAI)

        Args:
            url: The URL this text came from
            text_content: Extracted text content from the URL (already processed for length)
        """
        return OpenAIEntityPrompts.get_entity_extraction_text_prompt(url, text_content, url_type)


