"""
API Response Logger

Utility for logging all Gemini and OpenAI API responses to JSON files
"""

import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path
import logging


class APIResponseLogger:
    """
    Logger for API responses from Gemini and OpenAI calls
    """
    
    def __init__(self, base_log_dir: str = "api_logs", max_file_size_mb: int = 10):
        """
        Initialize the API response logger
        
        Args:
            base_log_dir: Base directory for storing API logs
            max_file_size_mb: Maximum file size in MB for individual log files
        """
        self.base_log_dir = Path(base_log_dir)
        self.max_file_size_bytes = max_file_size_mb * 1024 * 1024
        self._ensure_log_directory()
        
        # Setup internal logger for this utility
        self._setup_internal_logger()
    
    def _ensure_log_directory(self):
        """Ensure the log directory exists"""
        try:
            self.base_log_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            print(f"Warning: Could not create log directory {self.base_log_dir}: {e}")
    
    def _setup_internal_logger(self):
        """Setup internal logger for this utility"""
        self.internal_logger = logging.getLogger('api_response_logger')
        self.internal_logger.setLevel(logging.WARNING)
        if not self.internal_logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.internal_logger.addHandler(handler)
    
    def _generate_filename(self, api_type: str, request_id: str = None) -> str:
        """
        Generate a unique filename for the log
        
        Args:
            api_type: Type of API (gemini/openai)
            request_id: Optional request ID for context
            
        Returns:
            Unique filename
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
        if request_id:
            return f"{api_type}_{request_id}_{timestamp}.json"
        return f"{api_type}_{timestamp}.json"
    
    def log_gemini_response(self, 
                           prompt: str, 
                           response: Any, 
                           model_name: str = None,
                           request_id: str = None,
                           context: Dict[str, Any] = None) -> str:
        """
        Log Gemini API response
        
        Args:
            prompt: The prompt sent to Gemini
            response: The response from Gemini
            model_name: The model used
            request_id: Optional request ID for context
            context: Additional context information
            
        Returns:
            Path to the created log file
        """
        log_data = {
            "api_type": "gemini",
            "timestamp": datetime.now().isoformat(),
            "model_name": model_name,
            "request_id": request_id,
            "context": context or {},
            "request": {
                "prompt": prompt
            },
            "response": response
        }
        
        filename = self._generate_filename("gemini", request_id)
        return self._write_log_file(filename, log_data)
    
    def log_openai_response(self, 
                           messages: list, 
                           response: Any,
                           name: str = None,
                           request_id: str = None,
                           context: Dict[str, Any] = None) -> str:
        """
        Log OpenAI API response
        
        Args:
            messages: The messages sent to OpenAI
            response: The response from OpenAI
            name: The name parameter used in the call
            request_id: Optional request ID for context
            context: Additional context information
            
        Returns:
            Path to the created log file
        """
        log_data = {
            "api_type": "openai",
            "timestamp": datetime.now().isoformat(),
            "name": name,
            "request_id": request_id,
            "context": context or {},
            "request": {
                "messages": messages
            },
            "response": response
        }
        
        filename = self._generate_filename("openai", request_id)
        return self._write_log_file(filename, log_data)
    
    def _sanitize_data_for_logging(self, data: Any) -> Any:
        """
        Sanitize data for logging by truncating large responses
        
        Args:
            data: Data to sanitize
            
        Returns:
            Sanitized data
        """
        try:
            # Convert to string to check size
            data_str = json.dumps(data, default=str)
            if len(data_str.encode('utf-8')) > self.max_file_size_bytes:
                # Truncate large responses
                if isinstance(data, dict):
                    truncated_data = data.copy()
                    truncated_data['_truncated'] = True
                    truncated_data['_original_size_bytes'] = len(data_str.encode('utf-8'))
                    # Keep only essential fields and truncate response
                    if 'response' in truncated_data:
                        response_str = str(truncated_data['response'])
                        if len(response_str) > 5000:
                            truncated_data['response'] = response_str[:5000] + "... [TRUNCATED]"
                    return truncated_data
                else:
                    return str(data)[:5000] + "... [TRUNCATED]"
            return data
        except Exception:
            return {"error": "Failed to sanitize data for logging", "original_type": str(type(data))}
    
    def _write_log_file(self, filename: str, log_data: Dict[str, Any]) -> str:
        """
        Write log data to file with enhanced error handling
        
        Args:
            filename: Name of the log file
            log_data: Data to log
            
        Returns:
            Path to the created log file
        """
        try:
            # Sanitize data before writing
            sanitized_data = self._sanitize_data_for_logging(log_data)
            
            file_path = self.base_log_dir / filename
            
            # Ensure directory exists before writing
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(sanitized_data, f, indent=2, ensure_ascii=False, default=str)
            
            return str(file_path)
            
        except PermissionError as e:
            self.internal_logger.warning(f"Permission denied writing to {filename}: {e}")
            return ""
        except OSError as e:
            self.internal_logger.warning(f"OS error writing to {filename}: {e}")
            return ""
        except json.JSONEncodeError as e:
            self.internal_logger.warning(f"JSON encoding error for {filename}: {e}")
            # Try to save a simplified version
            try:
                simplified_data = {
                    "error": "JSON encoding failed",
                    "timestamp": log_data.get("timestamp", datetime.now().isoformat()),
                    "api_type": log_data.get("api_type", "unknown"),
                    "request_id": log_data.get("request_id", "unknown")
                }
                file_path = self.base_log_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(simplified_data, f, indent=2, ensure_ascii=False)
                return str(file_path)
            except Exception:
                return ""
        except Exception as e:
            # If logging fails, we don't want to break the main functionality
            self.internal_logger.warning(f"Unexpected error writing to {filename}: {e}")
            return ""


# Global instance for use across the Extractor module
api_logger = APIResponseLogger()
