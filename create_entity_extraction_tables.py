#!/usr/bin/env python3
"""
Create entity extraction database tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import engine
from app.models.db_models import EntityExtractionAnalysis, EntityExtractionUrlAnalysis
from sqlmodel import SQLModel

def create_entity_extraction_tables():
    """Create the entity extraction tables"""
    
    print("Creating Entity Extractor database tables...")
    
    try:
        # Create the tables
        SQLModel.metadata.create_all(engine)
        print("✅ Entity extraction tables created successfully!")
        
        # Verify tables exist
        from sqlalchemy import text
        from app.database import get_session
        
        with next(get_session()) as session:
            # Check if tables exist
            tables_to_check = [
                'entity_extraction_analysis',
                'entity_extraction_url_analysis'
            ]
            
            for table_name in tables_to_check:
                result = session.exec(text(f"SHOW TABLES LIKE '{table_name}'"))
                exists = result.fetchone()
                
                if exists:
                    print(f"✅ Table '{table_name}' exists")
                    
                    # Show table structure
                    result = session.exec(text(f"DESCRIBE {table_name}"))
                    columns = result.fetchall()
                    print(f"   Columns ({len(columns)}):")
                    for column in columns[:5]:  # Show first 5 columns
                        print(f"     - {column[0]} ({column[1]})")
                    if len(columns) > 5:
                        print(f"     ... and {len(columns) - 5} more columns")
                else:
                    print(f"❌ Table '{table_name}' does not exist")
        
        print("\nTable creation completed!")
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")

if __name__ == "__main__":
    create_entity_extraction_tables()