nohup: ignoring input
/mnt/e/WebReview_DS_API_24Jun/uv/lib/python3.12/site-packages/celery/platforms.py:829: SecurityWarning: You're running the worker with superuser privileges: this is
absolutely not recommended!

Please specify a different user using the --uid option.

User information: uid=0 euid=0 gid=0 egid=0

  warnings.warn(SecurityWarning(ROOT_DISCOURAGED.format(
[2025-07-27 05:27:42,384: DEBUG/MainProcess] | Worker: Preparing bootsteps.
[2025-07-27 05:27:42,428: DEBUG/MainProcess] | Worker: Building graph...
[2025-07-27 05:27:42,429: DEBUG/MainProcess] | Worker: New boot order: {Timer, Hub, Pool, Autoscaler, Beat, StateDB, Consumer}
[2025-07-27 05:27:42,624: DEBUG/MainProcess] | Consumer: Preparing bootsteps.
[2025-07-27 05:27:42,624: DEBUG/MainProcess] | Consumer: Building graph...
[2025-07-27 05:27:42,802: DEBUG/MainProcess] | Consumer: New boot order: {Connection, Events, Mingle, Gossip, Heart, Agent, Tasks, Control, event loop}
 
 -------------- celery@LAPTOP-CCUBTI4C v5.4.0 (opalescent)
--- ***** ----- 
-- ******* ---- Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39 2025-07-27 05:27:42
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         analysis_tasks:0x7fc8bfd0b290
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 8 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                .> general_queue    exchange=general_queue(direct) key=general_queue
                .> mcc_queue        exchange=mcc_queue(direct) key=mcc_queue
                .> policy_queue     exchange=policy_queue(direct) key=policy_queue
                .> risky_queue      exchange=risky_queue(direct) key=risky_queue

[tasks]
  . celery.accumulate
  . celery.backend_cleanup
  . celery.chain
  . celery.chord
  . celery.chord_unlock
  . celery.chunks
  . celery.group
  . celery.map
  . celery.starmap
  . health_check_mcc
  . health_check_url_classification
  . process_mcc_analysis
  . process_policy_analysis
  . process_policy_analysis_enhanced
  . process_risky_classification
  . process_url_classification
  . process_url_classification_v2
  . retry_mcc_analysis
  . retry_url_classification
  . test_task

[2025-07-27 05:27:42,925: DEBUG/MainProcess] | Worker: Starting Hub
[2025-07-27 05:27:42,925: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:42,925: DEBUG/MainProcess] | Worker: Starting Pool
[2025-07-27 05:27:44,302: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:44,303: DEBUG/MainProcess] | Worker: Starting Consumer
[2025-07-27 05:27:44,304: DEBUG/MainProcess] | Consumer: Starting Connection
[2025-07-27 05:27:44,326: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-27 05:27:44,326: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:44,327: DEBUG/MainProcess] | Consumer: Starting Events
[2025-07-27 05:27:44,328: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:44,328: DEBUG/MainProcess] | Consumer: Starting Mingle
[2025-07-27 05:27:44,328: INFO/MainProcess] mingle: searching for neighbors
[2025-07-27 05:27:45,335: INFO/MainProcess] mingle: all alone
[2025-07-27 05:27:45,336: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:45,336: DEBUG/MainProcess] | Consumer: Starting Gossip
[2025-07-27 05:27:45,340: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:45,341: DEBUG/MainProcess] | Consumer: Starting Heart
[2025-07-27 05:27:45,343: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:45,344: DEBUG/MainProcess] | Consumer: Starting Tasks
[2025-07-27 05:27:45,443: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:45,443: DEBUG/MainProcess] | Consumer: Starting Control
[2025-07-27 05:27:45,446: DEBUG/MainProcess] ^-- substep ok
[2025-07-27 05:27:45,447: DEBUG/MainProcess] | Consumer: Starting event loop
[2025-07-27 05:27:45,447: DEBUG/MainProcess] | Worker: Hub.register Pool...
[2025-07-27 05:27:45,448: INFO/MainProcess] celery@LAPTOP-CCUBTI4C ready.
[2025-07-27 05:27:45,448: DEBUG/MainProcess] basic.qos: prefetch_count->8
[2025-07-27 05:30:12,457: INFO/MainProcess] Task process_policy_analysis_enhanced[dd952dfc-0991-417f-9ddb-a2c77dd14f81] received
[2025-07-27 05:30:12,457: DEBUG/MainProcess] TaskPool: Apply <function fast_trace_task at 0x7fc8bfdd7b00> (args:('process_policy_analysis_enhanced', 'dd952dfc-0991-417f-9ddb-a2c77dd14f81', {'lang': 'py', 'task': 'process_policy_analysis_enhanced', 'id': 'dd952dfc-0991-417f-9ddb-a2c77dd14f81', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [2400, 2100], 'root_id': 'dd952dfc-0991-417f-9ddb-a2c77dd14f81', 'parent_id': None, 'argsrepr': '(995,)', 'kwargsrepr': '{}', 'origin': 'gen641@LAPTOP-CCUBTI4C', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'properties': {'correlation_id': 'dd952dfc-0991-417f-9ddb-a2c77dd14f81', 'reply_to': 'ec05bfe0-6542-3285-8da7-d92bddcdf082', 'delivery_mode': 2, 'delivery_info': {'exchange': '', 'routing_key': 'policy_queue'}, 'priority': 0, 'body_encoding': 'base64', 'delivery_tag': '94eb8327-b8f7-4531-b54e-554adb08223b'}, 'reply_to': 'ec05bfe0-6542-3285-8da7-d92bddcdf082', 'correlation_id': 'dd952dfc-0991-417f-9ddb-a2c77dd14f81', 'hostname': 'celery@LAPTOP-CCUBTI4C', 'delivery_info':... kwargs:{})
2025-07-27 05:30:12,542 INFO sqlalchemy.engine.Engine SELECT DATABASE()
[2025-07-27 05:30:12,542: INFO/ForkPoolWorker-8] SELECT DATABASE()
2025-07-27 05:30:12,543 INFO sqlalchemy.engine.Engine [raw sql] {}
[2025-07-27 05:30:12,543: INFO/ForkPoolWorker-8] [raw sql] {}
2025-07-27 05:30:12,558 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
[2025-07-27 05:30:12,558: INFO/ForkPoolWorker-8] SELECT @@sql_mode
2025-07-27 05:30:12,559 INFO sqlalchemy.engine.Engine [raw sql] {}
[2025-07-27 05:30:12,559: INFO/ForkPoolWorker-8] [raw sql] {}
2025-07-27 05:30:12,567 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
[2025-07-27 05:30:12,567: INFO/ForkPoolWorker-8] SELECT @@lower_case_table_names
2025-07-27 05:30:12,569 INFO sqlalchemy.engine.Engine [raw sql] {}
[2025-07-27 05:30:12,569: INFO/ForkPoolWorker-8] [raw sql] {}
2025-07-27 05:30:12,587 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:30:12,587: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:30:12,614 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
[2025-07-27 05:30:12,614: INFO/ForkPoolWorker-8] SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-27 05:30:12,617 INFO sqlalchemy.engine.Engine [generated in 0.00383s] {'pk_1': 995}
[2025-07-27 05:30:12,617: INFO/ForkPoolWorker-8] [generated in 0.00383s] {'pk_1': 995}
2025-07-27 05:30:12,630 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-27 05:30:12,630: INFO/ForkPoolWorker-8] ROLLBACK
[2025-07-27 05:30:12,645: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][process_cleanup][NO_REF] DEBUG: Exit cleanup registered
[2025-07-27 05:30:12,646: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: Starting enhanced policy analysis task for analysis_id: 995
2025-07-27 05:30:12,656 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:30:12,656: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:30:12,660 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
[2025-07-27 05:30:12,660: INFO/ForkPoolWorker-8] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-27 05:30:12,662 INFO sqlalchemy.engine.Engine [generated in 0.00173s] {'pk_1': 995}
[2025-07-27 05:30:12,662: INFO/ForkPoolWorker-8] [generated in 0.00173s] {'pk_1': 995}
[2025-07-27 05:30:12,673: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: Found PolicyAnalysisNew record: makemytrip.com, ref_id: 428220ca-60c7-4af6-ab0e-799e4ede7379
2025-07-27 05:30:12,676 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET started_at=%(started_at)s, processing_status=%(processing_status)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
[2025-07-27 05:30:12,676: INFO/ForkPoolWorker-8] UPDATE policy_analysis_new_gemini SET started_at=%(started_at)s, processing_status=%(processing_status)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
2025-07-27 05:30:12,677 INFO sqlalchemy.engine.Engine [generated in 0.00155s] {'started_at': '2025-07-27T05:30:12.674301Z', 'processing_status': 'PROCESSING', 'policy_analysis_new_gemini_id': 995}
[2025-07-27 05:30:12,677: INFO/ForkPoolWorker-8] [generated in 0.00155s] {'started_at': '2025-07-27T05:30:12.674301Z', 'processing_status': 'PROCESSING', 'policy_analysis_new_gemini_id': 995}
2025-07-27 05:30:12,687 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-27 05:30:12,687: INFO/ForkPoolWorker-8] COMMIT
[2025-07-27 05:30:12,704: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: Updated PolicyAnalysisNew status to PROCESSING
[2025-07-27 05:30:12,705: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: Initializing Enhanced Policy Analysis service
[2025-07-27 05:30:12,706: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Enhanced Policy Analysis Service initialized
{
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379",
  "org_id": "default",
  "required_categories": 6,
  "social_media_categories": 7
}
[2025-07-27 05:30:12,707: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: Running Enhanced Policy Analysis with conditional popup handling
[2025-07-27 05:30:12,708: DEBUG/ForkPoolWorker-8] Using selector: EpollSelector
[2025-07-27 05:30:12,709: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:30:12,710: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🚀 ENHANCED POLICY ANALYSIS STARTED (UNIFIED APPROACH)
[2025-07-27 05:30:12,710: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:30:12,711: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📋 Request ID: 428220ca-60c7-4af6-ab0e-799e4ede7379
[2025-07-27 05:30:12,711: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🏢 Organization: default
[2025-07-27 05:30:12,711: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:30:12,712: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:30:12,712: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📋 STEP 1: RETRIEVING URLs FROM DATABASE
[2025-07-27 05:30:12,712: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: --------------------------------------------------
2025-07-27 05:30:12,721 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:30:12,721: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:30:12,725 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
[2025-07-27 05:30:12,725: INFO/ForkPoolWorker-8] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-27 05:30:12,726 INFO sqlalchemy.engine.Engine [generated in 0.00088s] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
[2025-07-27 05:30:12,726: INFO/ForkPoolWorker-8] [generated in 0.00088s] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
2025-07-27 05:30:12,742 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
[2025-07-27 05:30:12,742: INFO/ForkPoolWorker-8] SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-27 05:30:12,743 INFO sqlalchemy.engine.Engine [generated in 0.00109s] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
[2025-07-27 05:30:12,743: INFO/ForkPoolWorker-8] [generated in 0.00109s] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
2025-07-27 05:30:12,756 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-27 05:30:12,756: INFO/ForkPoolWorker-8] ROLLBACK
[2025-07-27 05:30:12,771: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: URLs successfully retrieved
[2025-07-27 05:30:12,772: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Website: https://www.makemytrip.com/
[2025-07-27 05:30:12,772: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Depth 1 URLs: 172
[2025-07-27 05:30:12,773: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Depth 2 URLs: 0
[2025-07-27 05:30:12,773: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Total URLs: 172
[2025-07-27 05:30:12,774: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:30:12,774: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🏷️ STEP 2: CLASSIFYING URLs (UNIFIED SOFT → HARD)
[2025-07-27 05:30:12,775: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: --------------------------------------------------
[2025-07-27 05:30:12,776: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting URL classification using proper service methods
[2025-07-27 05:30:12,776: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting soft classification
{
  "website": "https://www.makemytrip.com/",
  "urls_depth_1_count": 172,
  "urls_depth_2_count": 0
}
[2025-07-27 05:30:12,831: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 172,
  "sample_urls": [
    "https://www.makemytrip.com/",
    "https://www.makemytrip.com/promos/jk-bank-offer-190225.html?detail_image=no",
    "https://www.facebook.com/makemytrip"
  ]
}
[2025-07-27 05:30:12,831: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:12][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://www.makemytrip.com/",
  "total_urls": 172
}
[2025-07-27 05:30:12,871: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): openaipublic.blob.core.windows.net:443
[2025-07-27 05:30:13,811: DEBUG/ForkPoolWorker-8] https://openaipublic.blob.core.windows.net:443 "GET /encodings/cl100k_base.tiktoken HTTP/1.1" 200 1681126
[2025-07-27 05:30:15,946: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:15][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=990, available_for_urls=87745
[2025-07-27 05:30:15,948: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:15][test-analysis][NO_REF] INFO: Total URL tokens: 4766, Available: 87745
[2025-07-27 05:30:15,949: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:15][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-27 05:30:15,950: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:15][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 172,
  "final_url_count": 172,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 990,
  "url_tokens": 4766,
  "final_total_tokens": 6021,
  "token_limit": 90000,
  "remaining_tokens": 83979
}
[2025-07-27 05:30:15,953: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:15][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 6869,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 3.121885299682617
}
[2025-07-27 05:30:16,712: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:16][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-27 05:30:16,713: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:16][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-27 05:30:16,718: DEBUG/ForkPoolWorker-8] Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'user', 'content': '  **Context*\n    You are an experienced Business Analayst who deals with the information around companies and its websites and therefore you are very well versed with the URLs.\n    You have all the capability to just look at the URL and decide on the categry of the URL, if its a\n        1. home page URL, OR\n        2. about us page URLs OR\n        3. contact us or customer support URLs OR\n        4. terms and condition URLs OR\n        5. privacy policy URLs OR\n        6. return, refund and cancellation URLs OR\n        7. products URLs\n        8. service URLs\n        9. delivery or shipping policy related URLs OR\n        10. catalouge URLs\n        11. instagram URL page of the business  \n        12. facebook URL page of the business \n        13. youtube URL page of the business \n        14. twitter URL page of the business \n        15. linkedin URL page of the business \n        16. pinterest URL page of the business\n\n    You will be given a website name and dictionary of URLs for that websites, dictionary will contain keys as an integer index and values as URL correspnding to the integer index.\n    \n    * Information on website and its URLs starts *\n    website name --> https://www.makemytrip.com/\n    dictionary of URLs of the website named "dictionary_of_URLs" --> {0: \'https://www.makemytrip.com/\', 1: \'https://www.makemytrip.com/promos/jk-bank-offer-190225.html?detail_image=no\', 2: \'https://www.facebook.com/makemytrip\', 3: \'https://platforms.makemytrip.com/contents/00afdadb-be69-44b4-848e-66795bd4a078\', 4: \'https://www.makemytrip.com/forex?utm_source=MMT&amp;utm_medium=mmt_app&amp;utm_campaign=ForexBrandCampaign_chiclet&amp;showHeader=false\', 5: \'https://www.makemytrip.com/tripideas/top-staycation-in-around-mumbai-for-weekend\', 6: \'https://www.makemytrip.com/tripideas/relaxation-destinations\', 7: \'https://cabs.makemytrip.com/?tripType=AT\', 8: \'https://www.makemytrip.com/promos/aadhaar-link-tatkal-bookings.html?detail_image=no\', 9: \'https://promos.makemytrip.com/appfest/2x//desktop-Monsoon-Camp-130625.jpg?im=Resize=\', 10: \'https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no\', 11: \'https://www.makemytrip.com/promos/bob-nce-********.html?detail_image=no\', 12: \'https://www.makemytrip.com/cards/makemytrip-icici-bank-credit-card\', 13: \'https://www.makemytrip.com/promos/intl-indian-food-201023.html\', 14: \'https://platforms.makemytrip.com/contents/7300a7ec-9452-4c2b-b2aa-e8b2ebee7f12\', 15: \'https://www.makemytrip.com/tripideas\', 16: \'https://www.makemytrip.com/tripideas/places/dooars\', 17: \'https://www.makemytrip.com/international-flights\', 18: \'https://www.makemytrip.com/promos/au-bank-cc-offer-310325.html?detail_image=no\', 19: \'https://www.makemytrip.com/promos/student-if-fest-160625.html?detail_image=no\', 20: \'https://www.makemytrip.com/promos/etihad-airways-010725.html?detail_image=no\', 21: \'https://bookmyforex.com/?utm_source=makemytrip&utm_medium=referral&utm_campaign=gommt-landing&utm_id=gommt-landing\', 22: \'https://www.makemytrip.com/homestays\', 23: \'https://www.makemytrip.com/promos/ancillary-meals.html?detail_image=no\', 24: \'https://www.makemytrip.com/tripideas/places/guntur\', 25: \'https://www.makemytrip.com/bus-tickets\', 26: \'https://www.makemytrip.com/promos/idfc-nce-********.html?detail_image=no\', 27: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PO2PHMPFEECD4_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MjcyLGFkSXRlbUlkOzIyMzYzLDIyMTU5NzM3NQ==\', 28: \'https://www.makemytrip.com/hotels/hotel-listing?_uCurrency=INR&amp;checkin=date_7&amp;checkout=date_8&amp;city=SFALLLA&amp;country=IN&amp;locusId=SFALLLA&amp;locusType=storefront&amp;roomStayQualifier=2e0e&amp;searchText=All%20The%20Lalit%20Groups%20Properties%20in%20India&amp;type=storefront&amp;detail_image=no\', 29: \'https://www.makemytrip.com/promos/hsbc-emi-offers-apr22.html?detail_image=no\', 30: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PX5ESOLUJ8B5K_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NzQ0LGFkSXRlbUlkOzIwMzY5LDk0NzE1MDEzMQ==\', 31: \'https://www.makemytrip.com/how2go\', 32: \'https://in.linkedin.com/company/makemytrip.com?open=outside\', 33: \'https://promos.makemytrip.com/bus-train-pass-tncs-080425.html?detail_image=no\', 34: \'https://www.makemytrip.com/tripideas/places/shoghi\', 35: \'https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&amp;display_image=no\', 36: \'https://www.makemytrip.com/tripideas/places/tadoba\', 37: \'https://www.makemytrip.com/tripideas/adventure-destinations\', 38: \'https://www.makemytrip.com/tripideas/top-staycation-in-around-bangalore-for-weekend\', 39: \'https://www.makemytrip.com/promos/gt-suhana-safar-sale-070725.html?detail_image=no\', 40: \'https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&display_image=no\', 41: \'https://www.makemytrip.com/?ccde=in&lang=hin\', 42: \'https://www.makemytrip.com/promos/gt-bus-trip-assured-030624.html?detail_image=no\', 43: \'https://www.makemytrip.com/forex?utm_source=MMT&utm_medium=mmt_app&utm_campaign=ForexBrandCampaign_chiclet&showHeader=false\', 44: \'https://visa.makemytrip.com/landing\', 45: \'https://www.makemytrip.com/tripideas/beach-destinations\', 46: \'https://www.makemytrip.com/promos/axis-offer-150524.html?detail_image=no\', 47: \'https://www.makemytrip.com/tripideas/places/dhanaulti\', 48: \'https://www.makemytrip.com/tripideas/heritage-destinations\', 49: \'https://www.makemytrip.com/promos/mmt-group-travel.html?detail_image=no\', 50: \'https://tripmoney.makemytrip.com/ext/api/v1/partners/mmt/initOTU?product=insurance&utm_source=secondaryicon&utm_medium=mmtpwa&utm_campaign=ins_mmt_pwa\', 51: \'https://www.makemytrip.com/promos/taj-stays-300625.html?detail_image=no\', 52: \'https://www.makemytrip.com/hotels/hotel-details/?hotelId=201212131521547548&amp;mtkeys=defaultMtkey&amp;_uCurrency=INR&amp;checkin=date_7&amp;checkout=date_8&amp;city=CTRNG&amp;country=IN&amp;&amp;locusId=CTRNG&amp;locusType=city&amp;rank=3&amp;roomStayQualifier=2e0e&amp;detail_image=no\', 53: \'https://www.makemytrip.com/tripideas/places/vellore\', 54: \'https://platforms.makemytrip.com/contents/f51d1b5c-1ade-464f-ac92-f8b5d36b6a2b\', 55: \'https://www.makemytrip.com/hotels/hotel-listing?_uCurrency=INR&checkin=date_7&checkout=date_8&city=SFALLLA&country=IN&locusId=SFALLLA&locusType=storefront&roomStayQualifier=2e0e&searchText=All%20The%20Lalit%20Groups%20Properties%20in%20India&type=storefront&detail_image=no\', 56: \'https://www.makemytrip.com/promos/if-trip-120124.html?detail_image=no\', 57: \'https://brands.makemytrip.com/Kingston?showHeader=false&open=browser\', 58: \'https://cabs.makemytrip.com/\', 59: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PX5ESOLUJ8B5K_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NzQ0LGFkSXRlbUlkOzIwMzY5LDk0NzE1MDEzMQ==\', 60: \'https://visa.makemytrip.com/\', 61: \'https://www.makemytrip.com/promos/gt-suhana-safar-sale-070725.html\', 62: \'https://x.com/makemytrip\', 63: \'https://www.makemytrip.com/flights\', 64: \'https://promos.makemytrip.com/appfest/2x//cred-116x116-********.jpg?im=Resize=\', 65: \'https://www.makemytrip.com/tripideas/places/mandarmani\', 66: \'https://promos.makemytrip.com/appfest/2x//student-on-board-dt-********.jpg?im=Resize=\', 67: \'https://www.makemytrip.com/promos/virgin-atlantic-offer-100325.html?detail_image=no\', 68: \'https://www.makemytrip.com/promos/canara-bank-offers-220723.html?detail_image=no\', 69: \'https://promos.makemytrip.com/images//Desktop-HongKong-30Jun.jpg?im=Resize=\', 70: \'https://www.makemytrip.com/promos/egypt-air-sale-010725.html?detail_image=no\', 71: \'https://www.makemytrip.com/promos/rupay-salary-offer-310823.html?detail_image=no\', 72: \'https://www.makemytrip.com/promos/rbl-nce-********.html?detail_image=no\', 73: \'https://www.makemytrip.com/holidays-india\', 74: \'https://www.makemytrip.com/tripideas/places/sasan-gir\', 75: \'https://www.makemytrip.com/promos/dh-icici-********.html?detail_image=no\', 76: \'https://www.makemytrip.com/tripideas/weekend-getaways\', 77: \'https://www.makemytrip.com/hotels/hotel-listing/?_uCurrency=INR&amp;checkin=date_7&amp;checkout=date_8&amp;city=SFROSE&amp;country=IN&amp;locusId=SFROSE&amp;locusType=storefront&amp;roomStayQualifier=2e0e&amp;searchText=All%20Shangri&nbsp;La\', 78: \'https://www.makemytrip.com/tripideas/places/bhandardara\', 79: \'https://www.makemytrip.com/\', 80: \'https://www.makemytrip.com/promos/qatar-airways-040725.html?detail_image=no\', 81: \'https://www.makemytrip.com/promos/train-seat-availability-forecast-230625.html?detail_image=no\', 82: \'https://www.makemytrip.com/tripideas/places/yelagiri\', 83: \'https://promos.makemytrip.com/mmt-south-bus-tncs-200524.html?detail_image=no\', 84: \'https://insurance.makemytrip.com/ext/api/v1/partners/mmt/initOTU?product=insurance&utm_source=secondaryicon&utm_medium=mmtpwa&utm_campaign=ins_mmt_pwa\', 85: \'https://www.makemytrip.com/promos/indigo-new-flights-080223.html?detail_image=no\', 86: \'https://www.makemytrip.com/railways\', 87: \'https://promos.makemytrip.com/mmtblack-bus-terms-071024.html?detail_image=no\', 88: \'https://www.makemytrip.com/tripideas/places/vagamon\', 89: \'https://www.makemytrip.com/promos/bhim-upi-offer-100125.html?detail_image=no\', 90: \'https://www.makemytrip.com/tripideas/pilgrimage-destinations\', 91: \'https://www.makemytrip.com/promos/cgh-hotel-sale-220823.html?detail_image=no\', 92: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PJP9S85UO5BM0_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NTE1LGFkSXRlbUlkOzE5NjkzLDI5NTEzNjI1\', 93: \'https://www.makemytrip.com/?_uCurrency=INR\', 94: \'https://www.makemytrip.com/promos/visa-signature-credit-cards-offer-280325.html?detail_image=no\', 95: \'https://www.makemytrip.com/tripideas/hills-mountains-destinations\', 96: \'https://www.makemytrip.com/promos/dh-regional-sale-01082024.html?detail_image=no\', 97: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PV4JS2GF66CQL_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MzU0LGFkSXRlbUlkOzIyNTY2LDY1NTYzNTQxMg==\', 98: \'https://www.makemytrip.com/promos/gt-at-cab-ride-guarantee-300125.html\', 99: \'https://www.makemytrip.com/promos/kenya-airways-030725.html?detail_image=no\', 100: \'https://www.makemytrip.com/forex\', 101: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PO2PHMPFEECD4_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MjcyLGFkSXRlbUlkOzIyMzYzLDIyMTU5NzM3NQ==\', 102: \'https://www.makemytrip.com/promos/yes-nce-offers-01072022.html?detail_image=no\', 103: \'https://www.makemytrip.com/railways/PNR\', 104: \'https://www.makemytrip.com/tripideas/places/narkanda\', 105: \'https://www.makemytrip.com/promos/malaysia-airline-210525.html?detail_image=no\', 106: \'https://www.makemytrip.com/hotels\', 107: \'https://brands.makemytrip.com/flight/airasia?showHeader=false&open=browser\', 108: \'https://www.instagram.com/makemytrip\', 109: \'https://brands.makemytrip.com/CGHEarth?open=browser\', 110: \'https://www.makemytrip.com/travel-guidelines/?schedule=webview\', 111: \'https://www.makemytrip.com/promos/air-astana-sale-010725.html?detail_image=no\', 112: \'https://www.makemytrip.com/insurance\', 113: \'https://www.makemytrip.com/promos/oyofest-19072023.html?detail_image=no\', 114: \'https://www.makemytrip.com/promos/nepal-airlines-130625.html?detail_image=no\', 115: \'https://www.makemytrip.com/promos/ih-offer-100524.html?detail_image=no\', 116: \'https://www.makemytrip.com/tripideas/places/srisailam\', 117: \'https://www.makemytrip.com/promos/new-user-campaign.html?offer=df&detail_page=no\', 118: \'https://supportz.makemytrip.com/mweb/bookingSummary\', 119: \'https://www.makemytrip.com/tripideas/places/parwanoo\', 120: \'https://www.makemytrip.com/tripideas/places/pachmarhi\', 121: \'https://www.makemytrip.com/promos/if-icici-060224.html?detail_image=no\', 122: \'https://www.makemytrip.com/support/mysafety/mysafety.php\', 123: \'https://www.makemytrip.com/promos/sonar-bangla-hotel-offer-200224.html?detail_image=no\', 124: \'https://platforms.makemytrip.com/contents/07e30964-bcda-4c81-873f-fbb283b1845f\', 125: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PV4JS2GF66CQL_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MzU0LGFkSXRlbUlkOzIyNTY2LDY1NTYzNTQxMg==\', 126: \'https://cabs.makemytrip.com/self-drive\', 127: \'https://www.makemytrip.com/tripideas/places/saputara\', 128: \'https://www.makemytrip.com/promos/monsoon-stays-offer-020725.html?detail_image=no\', 129: \'https://platforms.makemytrip.com/contents/8db292f3-fd5a-448c-9f2a-58c78e10f56c\', 130: \'https://www.makemytrip.com/promos/cred-upi-flight-offer-250625.html?detail_image=no\', 131: \'https://www.makemytrip.com/?referrer=gcWrapper\', 132: \'https://www.makemytrip.com/promos/fern-hotels-sale-231023.html?detail_image=no\', 133: \'https://www.makemytrip.com/akam/13/pixel_41767149?a=dD02ODViNzk2ODNjNzFiM2IyOTRlZTEzOTgxMmI0MzQ1MjJmNzQxYzZhJmpzPW9mZg==\', 134: \'https://www.makemytrip.com/cabs\', 135: \'https://holidayz.makemytrip.com/holidays/india/package?id=55204&fromCity=New%20Delhi&pkgType=FIT&listingClassId=4299&depDate=2025-05-26&searchDate=2025-05-26&glp=true&room=2%2C0%2C0%2C0%2C%2C%2C&variantId=FLIGHT_1DB875FA&detail_image=no\', 136: \'https://www.makemytrip.com/tripideas/top-staycation-in-around-delhi-for-weekend\', 137: \'https://holidayz.makemytrip.com/holidays/international\', 138: \'https://promos.makemytrip.com/trains-mmtalways-051124.html?detail_image=no&tab=trains\', 139: \'https://promos.makemytrip.com/notification/xhdpi//Desktop-ApartmentSale-17Apr.jpg?im=Resize=\', 140: \'https://promos.makemytrip.com/bus-flash-deals-tncs-031024.html?detail_image=no\', 141: \'https://www.makemytrip.com/tripideas/places/araku-valley\', 142: \'https://www.makemytrip.com/tripideas/places/yercaud\', 143: \'https://www.makemytrip.com/support/contact-us.php\', 144: \'https://www.makemytrip.com/tripideas/places/udupi\', 145: \'https://brands.makemytrip.com/flight/cathay?open=browser&showHeader=false\', 146: \'https://www.makemytrip.com/hotels/hotel-details/?hotelId=201212131521547548&mtkeys=defaultMtkey&_uCurrency=INR&checkin=date_7&checkout=date_8&city=CTRNG&country=IN&&locusId=CTRNG&locusType=city&rank=3&roomStayQualifier=2e0e&detail_image=no\', 147: \'https://www.makemytrip.com/mice\', 148: \'https://www.makemytrip.com/promos/df-icici-********.html?detail_image=no\', 149: \'https://adorch.makemytrip.com/ext/user/track?type=view&trackingId=TU1UOzQ1NjgxMzUyMDAwMjAwMjgyNTIyNw==\', 150: \'https://platforms.makemytrip.com/contents/fb0b8518-346f-4c5d-b86e-bc520d8111cf\', 151: \'https://www.makemytrip.com/hotels/hotel-listing/?_uCurrency=INR&checkin=date_7&checkout=date_8&city=SFROSE&country=IN&locusId=SFROSE&locusType=storefront&roomStayQualifier=2e0e&searchText=All%20Shangri\', 152: \'https://www.makemytrip.com/promos/new-user-campaign.html?offer=df&amp;detail_page=no\', 153: \'https://www.makemytrip.com/promos/ama-stays-131024.html?detail_image=no\', 154: \'https://www.makemytrip.com/tripideas/places/dapoli\', 155: \'https://www.makemytrip.com/mbus\', 156: \'https://adorch.makemytrip.com/ext/user/track?type=click&trackingId=TU1UOzQ1NjgxMzUyMDAwMjAwMjgyNTIyNw==\', 157: \'https://www.makemytrip.com/promos/singapore-airlines-030625.html?detail_image=no\', 158: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PJP9S85UO5BM0_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NTE1LGFkSXRlbUlkOzE5NjkzLDI5NTEzNjI1\', 159: \'https://platforms.makemytrip.com/contents/3db1e6f2-270b-4783-8330-710d4950f6b0\', 160: \'https://www.makemytrip.com/tripideas/places/malvan\', 161: \'https://www.makemytrip.com/gift-cards\', 162: \'https://promos.makemytrip.com/appfest/2x//taj-116x116-03072025.jpg?im=Resize=\', 163: \'https://www.makemytrip.com/promos/au-offer-020124.html?detail_image=no\', 164: \'https://www.makemytrip.com/promos/federal-offer-050624.html?detail_image=no\', 165: \'https://promos.makemytrip.com/trains-mmtalways-051124.html?detail_image=no&amp;tab=trains\', 166: \'https://www.makemytrip.com/promos/top-rated-homestays-060725.html?detail_image=no\', 167: \'https://www.makemytrip.com/tripideas/places/dandeli\', 168: \'https://promos.makemytrip.com/mmt-travel-trends-report-apr24.pdf\', 169: \'https://holidayz.makemytrip.com/holidays/india/package?id=55204&amp;fromCity=New%20Delhi&amp;pkgType=FIT&amp;listingClassId=4299&amp;depDate=2025-05-26&amp;searchDate=2025-05-26&amp;glp=true&amp;room=2%2C0%2C0%2C0%2C%2C%2C&amp;variantId=FLIGHT_1DB875FA&amp;detail_image=no\', 170: \'https://www.makemytrip.com/promos/mmt-icici-cards.html\', 171: \'https://www.makemytrip.com/tripideas/places/vengurla\'}\n    "dictionary_of_URLs" will contain following information\n        a. key will be an integer\n        b. value will be the URL corresponding to this integer key\n    * Information on website and URLs ends *\n\n\n    * Tasks Starts *\n    You will have to categorize these website\'s URLs into the following categories.\n    [\'home_page\',\'about_us\',\'terms_and_condition\',\'returns_cancellation_exchange\',\'privacy_policy\',\'shipping_delivery\',\'contact_us\',\'products\',\'services\',\'catalogue\',\'instagram_page\',\'facebook_page\',\'youtube_page\',\'twitter_page\',\'linkedin_page\',\'pinterest_page\']\n    You have to extract the index corresponding to these categories.\n    You will have to output just index that correspond the correct URLs.\n\n\n    * Guidelines for the task *\n    1. Please go through all the key words mentioned in the URLs and think of a logical reason why it can be classified into these URLs.\n    2. home page is generally the landing page and also the shortest URL amongst all the URL.\n    3. about us page will have companies information and not founders or people information on it.\n    4. Cataloge URL will be the one having all categories of products and services at one place. Assign catalogue URL only whan you are very very sure about it.\n    5. Cataloge URL can be something like, "collections", "shop", "all products" , "all services" etc.\n    6. Extract only 5 random indexes of product and service pages. These index should have maximum possible products and services. Try not to extract URLs which are very specific to just one product and service.\n    7. URLs containing social media links for the business will mostly have respective name in the URL, like instagram, facebook, twitter, linkedin, youtube, pinterest\n    8. LinkedIn page should be of the company/business. It should not be of the founder/people.\n    9. Social media URLs should be of the company or the business. DO NOT output the founder page for facebook, youtube and instagram. \n    10. If you have slighest of chance that a URL can lie in any of these catgories, please assign it to that category.\n    11. Do not output an index which is not there in any key.\n\n    *** Important Note *** --> Please note that one same URL can go to 2 or more categories.\n\n    * Tasks Ends *\n\n    Output shoud strictly be in a json format containing these 16 keys.\n            1. \'home_page\':[], a list of integers which represent the URLs in "dictionary_of_URL", -->  Stick to one URL, multiple home page URLs are not required.\n            2. \'about_us\':[], a list of integers which represent the URLs in "dictionary_of_URL", -->  Stick to one URL, multiple home page URLs are not required.\n            3. \'terms_and_condition\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            4. \'returns_cancellation_exchange\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            5. \'privacy_policy\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            6. \'shipping_delivery\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            7. \'contact_us\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            8. \'products\':[], a list of integers with maximum 5 entries which represent the URLs in "dictionary_of_URL"\n            9. \'services\':[], a list of integers with maximum 5 entries which represent the URLs in "dictionary_of_URL"\n            10.\'catalogue\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            11.\'instagram_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            12.\'facebook_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            13.\'twitter_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            14.\'linkedin_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            15.\'youtube_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            16.\'pinterest_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n\n            Output strictly should not contain any thing apart from is json.\n            Strictly avoid any keywords or string outsisde this list.\n    '}], 'model': 'gpt-4o', 'max_tokens': 6000, 'seed': 0, 'temperature': 0, 'top_p': 0.95}}
[2025-07-27 05:30:16,722: DEBUG/ForkPoolWorker-8] Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
[2025-07-27 05:30:16,723: DEBUG/ForkPoolWorker-8] connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=5.0 socket_options=None
[2025-07-27 05:30:16,749: DEBUG/ForkPoolWorker-8] connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fc8aa1cede0>
[2025-07-27 05:30:16,750: DEBUG/ForkPoolWorker-8] start_tls.started ssl_context=<ssl.SSLContext object at 0x7fc8aa1936d0> server_hostname='api.openai.com' timeout=5.0
[2025-07-27 05:30:16,774: DEBUG/ForkPoolWorker-8] start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fc8a9db66c0>
[2025-07-27 05:30:16,774: DEBUG/ForkPoolWorker-8] send_request_headers.started request=<Request [b'POST']>
[2025-07-27 05:30:16,775: DEBUG/ForkPoolWorker-8] send_request_headers.complete
[2025-07-27 05:30:16,775: DEBUG/ForkPoolWorker-8] send_request_body.started request=<Request [b'POST']>
[2025-07-27 05:30:16,776: DEBUG/ForkPoolWorker-8] send_request_body.complete
[2025-07-27 05:30:16,776: DEBUG/ForkPoolWorker-8] receive_response_headers.started request=<Request [b'POST']>
[2025-07-27 05:30:20,282: DEBUG/ForkPoolWorker-8] receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 27 Jul 2025 05:30:22 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'biztelai'), (b'openai-processing-ms', b'2624'), (b'openai-project', b'proj_6PLYrbQOGO2ZQ8mi69HcOdP8'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'2682'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'450000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'444825'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'690ms'), (b'x-request-id', b'req_7606002a05c1e3d34011dc148d46b912'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=1SYFJyHU62GB2BiL50IMWk9Xo7NtOVHT4yz365fQ2PI-1753594222-1.0.1.1-iYHFUzn5Jn34MAZt8Ft8EQ8fvEwMY1EUffo8T0s8aQUi_tbzV7_uRO4UO3BSaBJ5cRXZybLOvKCG4cmzBJRTfPFCZKpr15j4dDKKaJIBjzY; path=/; expires=Sun, 27-Jul-25 06:00:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=AwUysnXf7eQ.YDnN7hOllOPXHsctzUjkPWb8VjPTtSk-1753594222692-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9659be7bfb184473-BOM'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
[2025-07-27 05:30:20,285: INFO/ForkPoolWorker-8] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-27 05:30:20,286: DEBUG/ForkPoolWorker-8] receive_response_body.started request=<Request [b'POST']>
[2025-07-27 05:30:20,287: DEBUG/ForkPoolWorker-8] receive_response_body.complete
[2025-07-27 05:30:20,288: DEBUG/ForkPoolWorker-8] response_closed.started
[2025-07-27 05:30:20,289: DEBUG/ForkPoolWorker-8] response_closed.complete
[2025-07-27 05:30:20,289: DEBUG/ForkPoolWorker-8] HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers([('date', 'Sun, 27 Jul 2025 05:30:22 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('access-control-expose-headers', 'X-Request-ID'), ('openai-organization', 'biztelai'), ('openai-processing-ms', '2624'), ('openai-project', 'proj_6PLYrbQOGO2ZQ8mi69HcOdP8'), ('openai-version', '2020-10-01'), ('x-envoy-upstream-service-time', '2682'), ('x-ratelimit-limit-requests', '5000'), ('x-ratelimit-limit-tokens', '450000'), ('x-ratelimit-remaining-requests', '4999'), ('x-ratelimit-remaining-tokens', '444825'), ('x-ratelimit-reset-requests', '12ms'), ('x-ratelimit-reset-tokens', '690ms'), ('x-request-id', 'req_7606002a05c1e3d34011dc148d46b912'), ('strict-transport-security', 'max-age=31536000; includeSubDomains; preload'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=1SYFJyHU62GB2BiL50IMWk9Xo7NtOVHT4yz365fQ2PI-1753594222-1.0.1.1-iYHFUzn5Jn34MAZt8Ft8EQ8fvEwMY1EUffo8T0s8aQUi_tbzV7_uRO4UO3BSaBJ5cRXZybLOvKCG4cmzBJRTfPFCZKpr15j4dDKKaJIBjzY; path=/; expires=Sun, 27-Jul-25 06:00:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('x-content-type-options', 'nosniff'), ('set-cookie', '_cfuvid=AwUysnXf7eQ.YDnN7hOllOPXHsctzUjkPWb8VjPTtSk-1753594222692-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9659be7bfb184473-BOM'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
[2025-07-27 05:30:20,290: DEBUG/ForkPoolWorker-8] request_id: req_7606002a05c1e3d34011dc148d46b912
[2025-07-27 05:30:20,302: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:20][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=152, prompt_tokens=6791, total_tokens=6943, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
[2025-07-27 05:30:21,303: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Soft classification response received
{
  "response_length": 475,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [],\n    \"terms_and_condition\": [10, 33, 87],\n    \"returns_cancellation_exchange\": [],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [],\n    \"contac..."
}
[2025-07-27 05:30:21,305: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://www.makemytrip.com/",
  "dictionary_1_count": 172,
  "total_chars": 13605,
  "urls_sample": [
    "https://www.makemytrip.com/",
    "https://www.makemytrip.com/promos/jk-bank-offer-190225.html?detail_image=no",
    "https://www.facebook.com/makemytrip"
  ]
}
[2025-07-27 05:30:21,306: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [],
    "terms_and_condition": [
      10,
      33,
      87
    ],
    "returns_cancellation_exchange": [],
    "privacy_policy": [],
    "shipping_delivery": [],
    "contact_us": [
      143
    ],
    "products": [
      12,
      22,
      25,
      63,
      106
    ],
    "services": [
      7,
      73,
      86,
      112,
      134
    ],
    "catalogue": [],
    "instagram_page": [
      108
    ],
    "facebook_page": [
      2
    ],
    "twitter_page": [
      62
    ],
    "linkedin_page": [
      32
    ],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-27 05:30:21,415: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://www.makemytrip.com/"
    ],
    "about_us": [],
    "terms_and_condition": [
      "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
      "https://promos.makemytrip.com/bus-train-pass-tncs-080425.html?detail_image=no",
      "https://promos.makemytrip.com/mmtblack-bus-terms-071024.html?detail_image=no"
    ],
    "returns_cancellation_exchange": [],
    "privacy_policy": [],
    "shipping_delivery": [],
    "contact_us": [
      "https://www.makemytrip.com/support/contact-us.php"
    ],
    "products": [
      "https://www.makemytrip.com/cards/makemytrip-icici-bank-credit-card",
      "https://www.makemytrip.com/homestays",
      "https://www.makemytrip.com/bus-tickets",
      "https://www.makemytrip.com/flights",
      "https://www.makemytrip.com/hotels"
    ],
    "services": [
      "https://cabs.makemytrip.com/?tripType=AT",
      "https://www.makemytrip.com/holidays-india",
      "https://www.makemytrip.com/railways",
      "https://www.makemytrip.com/insurance",
      "https://www.makemytrip.com/cabs"
    ],
    "catalogue": [],
    "instagram_page": [
      "https://www.instagram.com/makemytrip"
    ],
    "facebook_page": [
      "https://www.facebook.com/makemytrip"
    ],
    "twitter_page": [
      "https://x.com/makemytrip"
    ],
    "linkedin_page": [
      "https://in.linkedin.com/company/makemytrip.com?open=outside"
    ],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 20,
  "priority_urls_count": 15
}
[2025-07-27 05:30:21,416: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: URL classification completed
[2025-07-27 05:30:21,416: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Categories found: 16
[2025-07-27 05:30:21,416: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📋 Categories: home_page, about_us, terms_and_condition, returns_cancellation_exchange, privacy_policy, shipping_delivery, contact_us, products, services, catalogue, instagram_page, facebook_page, twitter_page, linkedin_page, youtube_page, pinterest_page
[2025-07-27 05:30:21,417: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
🔀 STEP 3: DETERMINING PROCESSING FLOW
--------------------------------------------------
[2025-07-27 05:30:21,417: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ NORMAL FLOW SELECTED: All URLs are reachable
{
  "backup_needed": false,
  "trigger_reason": "UNREACHABLE_VIA_TOOL_IS_EMPTY",
  "unreachable_via_tool_count": 0,
  "flow_decision": "NORMAL_FLOW",
  "note": "Will use hard classification for policy + soft classification for social media"
}
[2025-07-27 05:30:21,418: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Reachability calculation completed
{
  "total_priority_urls": 4,
  "priority_urls_reachable": 4,
  "priority_urls_not_reachable": 0,
  "reachability_percentage": 100.0
}
[2025-07-27 05:30:21,418: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ DECISION: Using NORMAL FLOW (HARD CLASSIFICATION)
{
  "reason": "All URLs verified as reachable",
  "method": "Hard classification for policy, soft for social media",
  "reachability": "100.00%"
}
[2025-07-27 05:30:21,418: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Using hard classification results (social media already merged by URL classification service)
[2025-07-27 05:30:21,418: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Processing hard classification flow
[2025-07-27 05:30:21,419: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🎯 Starting representative URL selection with priority order implementation
{
  "total_categories": 16,
  "categories_with_urls": 9
}
[2025-07-27 05:30:21,419: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Priority selection: terms_and_condition -> terms_and_condition
{
  "selected_url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "total_urls_in_category": 3,
  "urls_skipped": 2,
  "priority_rank": 1
}
[2025-07-27 05:30:21,419: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Priority selection: contact_us -> contact_us
{
  "selected_url": "https://www.makemytrip.com/support/contact-us.php",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "priority_rank": 4
}
[2025-07-27 05:30:21,420: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: instagram_page -> instagram
{
  "selected_url": "https://www.instagram.com/makemytrip",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "instagram_page",
  "db_category": "instagram"
}
[2025-07-27 05:30:21,420: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: facebook_page -> facebook
{
  "selected_url": "https://www.facebook.com/makemytrip",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "facebook_page",
  "db_category": "facebook"
}
[2025-07-27 05:30:21,420: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: linkedin_page -> linkedin
{
  "selected_url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "linkedin_page",
  "db_category": "linkedin"
}
[2025-07-27 05:30:21,420: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: twitter_page -> twitter
{
  "selected_url": "https://x.com/makemytrip",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "twitter_page",
  "db_category": "twitter"
}
[2025-07-27 05:30:21,421: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🏁 Representative URL selection completed
{
  "selected_categories": 6,
  "categories": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter"
  ],
  "total_urls_selected": 6
}
[2025-07-27 05:30:21,421: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Representative URLs selected
{
  "categories_selected": 6,
  "categories": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter"
  ]
}
[2025-07-27 05:30:21,421: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🔄 Starting content extraction for all categories
{
  "total_categories": 6,
  "categories": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter"
  ]
}
[2025-07-27 05:30:21,421: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no"
}
[2025-07-27 05:30:21,422: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no"
}
[2025-07-27 05:30:21,422: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no"
}
[2025-07-27 05:30:21,422: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:30:21,423: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): promos.makemytrip.com:443
[2025-07-27 05:30:21,527: DEBUG/ForkPoolWorker-8] https://promos.makemytrip.com:443 "GET /mmt-north-bus-tncs-200524.html?detail_image=no HTTP/1.1" 200 4690
[2025-07-27 05:30:21,531: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "text_length": 2736,
  "method": "requests"
}
[2025-07-27 05:30:21,532: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:30:21,533: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:21][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no: 3000ms
[2025-07-27 05:30:38,560: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no
[2025-07-27 05:30:39,860: INFO/ForkPoolWorker-8] Navigating to https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no
[2025-07-27 05:30:40,217: INFO/ForkPoolWorker-8] Waiting 3000ms for images to load...
[2025-07-27 05:30:43,274: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:30:43,517: INFO/ForkPoolWorker-8] Screenshot captured in 4.96s, size: 151038 bytes
[2025-07-27 05:30:43,821: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:30:43,838: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png"
}
[2025-07-27 05:30:43,849: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '151038'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': 'd7591472-6aaa-11f0-a0e6-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:30:44,182: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:30:45 GMT'
    'Etag': '"0x8DDCCCEBC8358E5"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '25a22844-701e-0027-0cb7-feacd0000000'
    'x-ms-client-request-id': 'd7591472-6aaa-11f0-a0e6-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:30:44 GMT'
[2025-07-27 05:30:44,183: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png"
}
[2025-07-27 05:30:44,184: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png"
}
[2025-07-27 05:30:44,185: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "text_length": 2736,
  "screenshot_status": "success"
}
[2025-07-27 05:30:44,185: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "text_length": 2736,
  "has_screenshot": true
}
[2025-07-27 05:30:44,186: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php"
}
[2025-07-27 05:30:44,186: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php"
}
[2025-07-27 05:30:44,187: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://www.makemytrip.com/support/contact-us.php"
}
[2025-07-27 05:30:44,187: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:44][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:30:44,188: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): www.makemytrip.com:443
[2025-07-27 05:30:45,470: DEBUG/ForkPoolWorker-8] https://www.makemytrip.com:443 "GET /support/contact-us.php HTTP/1.1" 200 25836
[2025-07-27 05:30:45,493: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:45][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "text_length": 2808,
  "method": "requests"
}
[2025-07-27 05:30:45,494: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:45][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://www.makemytrip.com/support/contact-us.php: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:30:45,494: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:45][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://www.makemytrip.com/support/contact-us.php: 3000ms
[2025-07-27 05:30:50,734: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://www.makemytrip.com/support/contact-us.php
[2025-07-27 05:30:51,160: INFO/ForkPoolWorker-8] Navigating to https://www.makemytrip.com/support/contact-us.php
[2025-07-27 05:30:51,290: ERROR/ForkPoolWorker-8] Error capturing screenshot: Page.goto: net::ERR_HTTP2_PROTOCOL_ERROR at https://www.makemytrip.com/support/contact-us.php
Call log:
  - navigating to "https://www.makemytrip.com/support/contact-us.php", waiting until "domcontentloaded"

[2025-07-27 05:30:51,560: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:30:51,571: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Screenshot capture returned no data for https://www.makemytrip.com/support/contact-us.php
[2025-07-27 05:30:51,572: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "text_length": 2808,
  "screenshot_status": "failed"
}
[2025-07-27 05:30:51,572: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "text_length": 2808,
  "has_screenshot": false
}
[2025-07-27 05:30:51,572: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for instagram
{
  "url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:30:51,573: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for instagram
{
  "url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:30:51,573: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:30:51,574: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:51][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://www.instagram.com/makemytrip",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:30:51,574: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): www.instagram.com:443
[2025-07-27 05:30:51,788: DEBUG/ForkPoolWorker-8] https://www.instagram.com:443 "GET /makemytrip HTTP/1.1" 301 0
[2025-07-27 05:30:52,056: DEBUG/ForkPoolWorker-8] https://www.instagram.com:443 "GET /makemytrip/ HTTP/1.1" 200 None
[2025-07-27 05:30:52,147: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with requests returned insufficient data
[2025-07-27 05:30:52,147: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting playwright extraction
{
  "url": "https://www.instagram.com/makemytrip",
  "method": "playwright",
  "attempt": 2
}
[2025-07-27 05:30:52,148: WARNING/ForkPoolWorker-8] [2025-07-27 05:30:52][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.instagram.com/makemytrip",
  "timeout": 45
}
[2025-07-27 05:31:06,615: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:06][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:31:06,615: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:06][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.instagram.com/makemytrip",
  "retry": 0
}
[2025-07-27 05:31:06,732: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:06][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-27 05:31:41,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:41][text_extraction][NO_REF] ERROR: Failed to extract text with direct connection
{
  "error": "{'error': {'error': 'Page.goto: Timeout 30000ms exceeded.\\nCall log:\\n  - navigating to \"https://www.instagram.com/makemytrip\", waiting until \"load\"\\n', 'url': 'https://www.instagram.com/makemytrip', 'retry': 1}}"
}
[2025-07-27 05:31:41,566: WARNING/ForkPoolWorker-8] Traceback for analysis text_extraction:
[2025-07-27 05:31:41,567: WARNING/ForkPoolWorker-8] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-27 05:31:41,707: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:41][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-27 05:31:41,707: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:41][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-27 05:31:43,073: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:43][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://www.instagram.com/makemytrip
[2025-07-27 05:31:43,074: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with playwright returned insufficient data
[2025-07-27 05:31:43,074: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: All text extraction methods failed for https://www.instagram.com/makemytrip
[2025-07-27 05:31:43,074: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:43][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://www.instagram.com/makemytrip: close_popups=True (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:31:43,075: WARNING/ForkPoolWorker-8] [2025-07-27 05:31:43][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://www.instagram.com/makemytrip: 5000ms
[2025-07-27 05:31:47,894: ERROR/ForkPoolWorker-8] Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
[2025-07-27 05:31:47,895: INFO/ForkPoolWorker-8] Normalized URL: https://www.instagram.com/makemytrip -> https://www.instagram.com/makemytrip/
[2025-07-27 05:31:47,895: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://www.instagram.com/makemytrip/
[2025-07-27 05:31:48,308: INFO/ForkPoolWorker-8] Navigating to https://www.instagram.com/makemytrip/
[2025-07-27 05:31:52,000: INFO/ForkPoolWorker-8] Pressed Escape key to try closing popup
[2025-07-27 05:31:52,251: INFO/ForkPoolWorker-8] Handling Instagram-specific redirects and popups...
[2025-07-27 05:31:55,340: INFO/ForkPoolWorker-8] Current URL after initial load: https://www.instagram.com/makemytrip/
[2025-07-27 05:31:57,755: INFO/ForkPoolWorker-8] Final URL: https://www.instagram.com/makemytrip/
[2025-07-27 05:31:57,755: INFO/ForkPoolWorker-8] Successfully navigated past Instagram login requirements
[2025-07-27 05:31:58,985: INFO/ForkPoolWorker-8] Waiting 5000ms for images to load...
[2025-07-27 05:32:04,035: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:32:04,206: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:32:04,206: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:32:04,353: INFO/ForkPoolWorker-8] Screenshot captured in 16.46s, size: 16885 bytes
[2025-07-27 05:32:04,625: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:32:04,641: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png"
}
[2025-07-27 05:32:04,643: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '16885'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '078273d5-6aab-11f0-8567-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:32:04,811: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:32:07 GMT'
    'Etag': '"0x8DDCCCEED364388"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': 'fb9b3afa-e01e-0057-4ab7-fe1527000000'
    'x-ms-client-request-id': '078273d5-6aab-11f0-8567-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:32:06 GMT'
[2025-07-27 05:32:04,813: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png"
}
[2025-07-27 05:32:04,815: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://www.instagram.com/makemytrip
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png"
}
[2025-07-27 05:32:04,816: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for instagram
{
  "url": "https://www.instagram.com/makemytrip",
  "text_length": 22,
  "screenshot_status": "success"
}
[2025-07-27 05:32:04,817: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for instagram
{
  "url": "https://www.instagram.com/makemytrip",
  "text_length": 0,
  "has_screenshot": true
}
[2025-07-27 05:32:04,818: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for facebook
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:32:04,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for facebook
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:32:04,820: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:32:04,821: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:04][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://www.facebook.com/makemytrip",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:32:04,824: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): www.facebook.com:443
[2025-07-27 05:32:05,095: DEBUG/ForkPoolWorker-8] https://www.facebook.com:443 "GET /makemytrip HTTP/1.1" 400 838
[2025-07-27 05:32:05,096: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:05][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Requests-based text extraction failed: 400 Client Error: Bad Request for url: https://www.facebook.com/makemytrip
[2025-07-27 05:32:05,097: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:05][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with requests returned insufficient data
[2025-07-27 05:32:05,098: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:05][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting playwright extraction
{
  "url": "https://www.facebook.com/makemytrip",
  "method": "playwright",
  "attempt": 2
}
[2025-07-27 05:32:05,098: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:05][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.facebook.com/makemytrip",
  "timeout": 45
}
[2025-07-27 05:32:16,092: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:16][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:32:16,092: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:16][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.facebook.com/makemytrip",
  "retry": 0
}
[2025-07-27 05:32:16,209: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:16][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-27 05:32:18,748: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:18][text_extraction][NO_REF] ERROR: Failed to extract text with direct connection
{
  "error": "{'error': 'Page.goto: net::ERR_ABORTED at https://www.facebook.com/makemytrip\\nCall log:\\n  - navigating to \"https://www.facebook.com/makemytrip\", waiting until \"load\"\\n', 'url': 'https://www.facebook.com/makemytrip', 'retry': 1, 'error_type': 'Error'}"
}
[2025-07-27 05:32:18,749: WARNING/ForkPoolWorker-8] Traceback for analysis text_extraction:
[2025-07-27 05:32:18,749: WARNING/ForkPoolWorker-8] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-27 05:32:18,895: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:18][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-27 05:32:18,895: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:18][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-27 05:32:56,097: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:56][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://www.facebook.com/makemytrip
[2025-07-27 05:32:56,097: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:56][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with playwright returned insufficient data
[2025-07-27 05:32:56,098: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:56][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: All text extraction methods failed for https://www.facebook.com/makemytrip
[2025-07-27 05:32:56,098: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:56][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://www.facebook.com/makemytrip: close_popups=True (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:32:56,098: WARNING/ForkPoolWorker-8] [2025-07-27 05:32:56][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://www.facebook.com/makemytrip: 4000ms
[2025-07-27 05:33:02,047: ERROR/ForkPoolWorker-8] Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
[2025-07-27 05:33:02,048: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://www.facebook.com/makemytrip
[2025-07-27 05:33:02,482: INFO/ForkPoolWorker-8] Navigating to https://www.facebook.com/makemytrip
[2025-07-27 05:33:06,263: INFO/ForkPoolWorker-8] Pressed Escape key to try closing popup
[2025-07-27 05:33:06,517: INFO/ForkPoolWorker-8] Handling Facebook-specific popups...
[2025-07-27 05:33:06,868: INFO/ForkPoolWorker-8] Waiting 4000ms for images to load...
[2025-07-27 05:33:10,926: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:33:11,103: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:33:11,104: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:33:11,272: INFO/ForkPoolWorker-8] Screenshot captured in 9.22s, size: 13630 bytes
[2025-07-27 05:33:11,570: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:33:11,588: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png"
}
[2025-07-27 05:33:11,589: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '13630'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '2f69c30b-6aab-11f0-b9cd-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:33:11,761: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:33:13 GMT'
    'Etag': '"0x8DDCCCF14EE7EDB"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': 'be474f73-001e-0002-55b7-fe05ac000000'
    'x-ms-client-request-id': '2f69c30b-6aab-11f0-b9cd-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:33:13 GMT'
[2025-07-27 05:33:11,763: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png"
}
[2025-07-27 05:33:11,765: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://www.facebook.com/makemytrip
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png"
}
[2025-07-27 05:33:11,766: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for facebook
{
  "url": "https://www.facebook.com/makemytrip",
  "text_length": 22,
  "screenshot_status": "success"
}
[2025-07-27 05:33:11,767: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for facebook
{
  "url": "https://www.facebook.com/makemytrip",
  "text_length": 0,
  "has_screenshot": true
}
[2025-07-27 05:33:11,768: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside"
}
[2025-07-27 05:33:11,768: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside"
}
[2025-07-27 05:33:11,769: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://in.linkedin.com/company/makemytrip.com?open=outside"
}
[2025-07-27 05:33:11,770: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:11][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:33:11,772: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): in.linkedin.com:443
[2025-07-27 05:33:13,129: DEBUG/ForkPoolWorker-8] https://in.linkedin.com:443 "GET /company/makemytrip.com?open=outside HTTP/1.1" 200 40651
[2025-07-27 05:33:13,466: DEBUG/ForkPoolWorker-8] Encoding detection: utf_8 is most likely the one.
[2025-07-27 05:33:13,536: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:13][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "text_length": 16252,
  "method": "requests"
}
[2025-07-27 05:33:13,537: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:13][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://in.linkedin.com/company/makemytrip.com?open=outside: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:33:13,538: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:13][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://in.linkedin.com/company/makemytrip.com?open=outside: 4000ms
[2025-07-27 05:33:18,193: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://in.linkedin.com/company/makemytrip.com?open=outside
[2025-07-27 05:33:18,629: INFO/ForkPoolWorker-8] Navigating to https://in.linkedin.com/company/makemytrip.com?open=outside
[2025-07-27 05:33:19,777: INFO/ForkPoolWorker-8] Waiting 4000ms for images to load...
[2025-07-27 05:33:26,808: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:33:26,985: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:33:26,986: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:33:28,557: INFO/ForkPoolWorker-8] Screenshot captured in 10.36s, size: 1788330 bytes
[2025-07-27 05:33:28,828: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:33:28,843: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:28][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png"
}
[2025-07-27 05:33:28,845: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '1788330'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '39b2b3f7-6aab-11f0-9bc9-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:33:29,060: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:33:29 GMT'
    'Etag': '"0x8DDCCCF1E440CB5"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '95ecfa2a-a01e-000b-41b7-fe407f000000'
    'x-ms-client-request-id': '39b2b3f7-6aab-11f0-9bc9-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:33:28 GMT'
[2025-07-27 05:33:29,061: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png"
}
[2025-07-27 05:33:29,062: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://in.linkedin.com/company/makemytrip.com?open=outside
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png"
}
[2025-07-27 05:33:29,063: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "text_length": 16252,
  "screenshot_status": "success"
}
[2025-07-27 05:33:29,063: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "text_length": 16252,
  "has_screenshot": true
}
[2025-07-27 05:33:29,064: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for twitter
{
  "url": "https://x.com/makemytrip"
}
[2025-07-27 05:33:29,065: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for twitter
{
  "url": "https://x.com/makemytrip"
}
[2025-07-27 05:33:29,066: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://x.com/makemytrip"
}
[2025-07-27 05:33:29,066: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://x.com/makemytrip",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:33:29,067: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): x.com:443
[2025-07-27 05:33:29,407: DEBUG/ForkPoolWorker-8] https://x.com:443 "GET /makemytrip HTTP/1.1" 200 None
[2025-07-27 05:33:29,426: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://x.com/makemytrip",
  "text_length": 492,
  "method": "requests"
}
[2025-07-27 05:33:29,427: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://x.com/makemytrip: close_popups=True (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:33:29,428: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:29][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://x.com/makemytrip: 3500ms
[2025-07-27 05:33:34,242: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://x.com/makemytrip
[2025-07-27 05:33:34,655: INFO/ForkPoolWorker-8] Navigating to https://x.com/makemytrip
[2025-07-27 05:33:37,860: INFO/ForkPoolWorker-8] Pressed Escape key to try closing popup
[2025-07-27 05:33:38,134: INFO/ForkPoolWorker-8] Handling X/Twitter-specific popups...
[2025-07-27 05:33:38,763: INFO/ForkPoolWorker-8] Waiting 3500ms for images to load...
[2025-07-27 05:33:42,317: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:33:42,505: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:33:42,506: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:33:42,777: INFO/ForkPoolWorker-8] Screenshot captured in 8.53s, size: 24773 bytes
[2025-07-27 05:33:43,170: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:33:43,188: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594423_0e71e1e2-bf65-4233-b02e-ee634060573a.png"
}
[2025-07-27 05:33:43,190: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594423_0e71e1e2-bf65-4233-b02e-ee634060573a.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '24773'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '423f9d35-6aab-11f0-8def-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:33:43,292: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:33:44 GMT'
    'Etag': '"0x8DDCCCF2781D025"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': 'f0456f16-501e-0020-49b8-fec0b3000000'
    'x-ms-client-request-id': '423f9d35-6aab-11f0-8def-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:33:44 GMT'
[2025-07-27 05:33:43,293: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594423_0e71e1e2-bf65-4233-b02e-ee634060573a.png"
}
[2025-07-27 05:33:43,293: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://x.com/makemytrip
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594423_0e71e1e2-bf65-4233-b02e-ee634060573a.png"
}
[2025-07-27 05:33:43,294: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for twitter
{
  "url": "https://x.com/makemytrip",
  "text_length": 492,
  "screenshot_status": "success"
}
[2025-07-27 05:33:43,295: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for twitter
{
  "url": "https://x.com/makemytrip",
  "text_length": 492,
  "has_screenshot": true
}
[2025-07-27 05:33:43,296: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Content extraction completed for all categories
{
  "categories_processed": 13,
  "categories_with_content": 6
}
[2025-07-27 05:33:43,296: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Hard classification flow completed
{
  "categories_processed": 13
}
[2025-07-27 05:33:43,296: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:33:43,296: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 💾 STEP 4: SAVING ANALYSIS RESULTS
[2025-07-27 05:33:43,297: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ----------------------------------------
2025-07-27 05:33:43,305 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:33:43,305: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:33:43,307 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
[2025-07-27 05:33:43,307: INFO/ForkPoolWorker-8] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-27 05:33:43,308 INFO sqlalchemy.engine.Engine [cached since 193s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
[2025-07-27 05:33:43,308: INFO/ForkPoolWorker-8] [cached since 193s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
2025-07-27 05:33:43,320 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET reachability_percentage=%(reachability_percentage)s, total_urls_processed=%(total_urls_processed)s, returns_cancellation_exchange_url=%(returns_cancellation_exchange_url)s, returns_cancellation_exchange_text=%(returns_cancellation_exchange_text)s, returns_cancellation_exchange_screenshot=%(returns_cancellation_exchange_screenshot)s, privacy_policy_url=%(privacy_policy_url)s, privacy_policy_text=%(privacy_policy_text)s, privacy_policy_screenshot=%(privacy_policy_screenshot)s, terms_and_condition_url=%(terms_and_condition_url)s, terms_and_condition_text=%(terms_and_condition_text)s, terms_and_condition_screenshot=%(terms_and_condition_screenshot)s, shipping_delivery_url=%(shipping_delivery_url)s, shipping_delivery_text=%(shipping_delivery_text)s, shipping_delivery_screenshot=%(shipping_delivery_screenshot)s, contact_us_url=%(contact_us_url)s, contact_us_text=%(contact_us_text)s, contact_us_screenshot=%(contact_us_screenshot)s, about_us_url=%(about_us_url)s, about_us_text=%(about_us_text)s, about_us_screenshot=%(about_us_screenshot)s, instagram_url=%(instagram_url)s, instagram_text=%(instagram_text)s, instagram_screenshot=%(instagram_screenshot)s, youtube_url=%(youtube_url)s, youtube_text=%(youtube_text)s, youtube_screenshot=%(youtube_screenshot)s, facebook_url=%(facebook_url)s, facebook_text=%(facebook_text)s, facebook_screenshot=%(facebook_screenshot)s, twitter_url=%(twitter_url)s, twitter_text=%(twitter_text)s, twitter_screenshot=%(twitter_screenshot)s, linkedin_url=%(linkedin_url)s, linkedin_text=%(linkedin_text)s, linkedin_screenshot=%(linkedin_screenshot)s, pinterest_url=%(pinterest_url)s, pinterest_text=%(pinterest_text)s, pinterest_screenshot=%(pinterest_screenshot)s, x_url=%(x_url)s, x_text=%(x_text)s, x_screenshot=%(x_screenshot)s, completed_at=%(completed_at)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
[2025-07-27 05:33:43,320: INFO/ForkPoolWorker-8] UPDATE policy_analysis_new_gemini SET reachability_percentage=%(reachability_percentage)s, total_urls_processed=%(total_urls_processed)s, returns_cancellation_exchange_url=%(returns_cancellation_exchange_url)s, returns_cancellation_exchange_text=%(returns_cancellation_exchange_text)s, returns_cancellation_exchange_screenshot=%(returns_cancellation_exchange_screenshot)s, privacy_policy_url=%(privacy_policy_url)s, privacy_policy_text=%(privacy_policy_text)s, privacy_policy_screenshot=%(privacy_policy_screenshot)s, terms_and_condition_url=%(terms_and_condition_url)s, terms_and_condition_text=%(terms_and_condition_text)s, terms_and_condition_screenshot=%(terms_and_condition_screenshot)s, shipping_delivery_url=%(shipping_delivery_url)s, shipping_delivery_text=%(shipping_delivery_text)s, shipping_delivery_screenshot=%(shipping_delivery_screenshot)s, contact_us_url=%(contact_us_url)s, contact_us_text=%(contact_us_text)s, contact_us_screenshot=%(contact_us_screenshot)s, about_us_url=%(about_us_url)s, about_us_text=%(about_us_text)s, about_us_screenshot=%(about_us_screenshot)s, instagram_url=%(instagram_url)s, instagram_text=%(instagram_text)s, instagram_screenshot=%(instagram_screenshot)s, youtube_url=%(youtube_url)s, youtube_text=%(youtube_text)s, youtube_screenshot=%(youtube_screenshot)s, facebook_url=%(facebook_url)s, facebook_text=%(facebook_text)s, facebook_screenshot=%(facebook_screenshot)s, twitter_url=%(twitter_url)s, twitter_text=%(twitter_text)s, twitter_screenshot=%(twitter_screenshot)s, linkedin_url=%(linkedin_url)s, linkedin_text=%(linkedin_text)s, linkedin_screenshot=%(linkedin_screenshot)s, pinterest_url=%(pinterest_url)s, pinterest_text=%(pinterest_text)s, pinterest_screenshot=%(pinterest_screenshot)s, x_url=%(x_url)s, x_text=%(x_text)s, x_screenshot=%(x_screenshot)s, completed_at=%(completed_at)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
2025-07-27 05:33:43,321 INFO sqlalchemy.engine.Engine [generated in 0.00103s] {'reachability_percentage': 100.0, 'total_urls_processed': 13, 'returns_cancellation_exchange_url': 'not_found', 'returns_cancellation_exchange_text': 'not_applicable', 'returns_cancellation_exchange_screenshot': 'not_applicable', 'privacy_policy_url': 'not_found', 'privacy_policy_text': 'not_applicable', 'privacy_policy_screenshot': 'not_applicable', 'terms_and_condition_url': 'https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no', 'terms_and_condition_text': "MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare ( ... (2438 characters truncated) ... he right to deny the offer against such bookings or to cancel such bookings. For such cases, MakeMyTrip shall not refund the booking amount. Book Now", 'terms_and_condition_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png', 'shipping_delivery_url': 'not_found', 'shipping_delivery_text': 'not_applicable', 'shipping_delivery_screenshot': 'not_applicable', 'contact_us_url': 'https://www.makemytrip.com/support/contact-us.php', 'contact_us_text': "Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugr ... (2510 characters truncated) ... f the cheapest flight of your choice today while also enjoying the other available options for your travel needs with us. © 2025 MAKEMYTRIP PVT. LTD.", 'contact_us_screenshot': 'screenshot_failed', 'about_us_url': 'not_found', 'about_us_text': 'not_applicable', 'about_us_screenshot': 'not_applicable', 'instagram_url': 'https://www.instagram.com/makemytrip', 'instagram_text': 'text_extraction_failed', 'instagram_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png', 'youtube_url': 'not_found', 'youtube_text': 'not_applicable', 'youtube_screenshot': 'not_applicable', 'facebook_url': 'https://www.facebook.com/makemytrip', 'facebook_text': 'text_extraction_failed', 'facebook_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png', 'twitter_url': 'https://x.com/makemytrip', 'twitter_text': 'JavaScript is not available. We’ve detected that JavaScript is disabled in this browser. Please enable JavaScript or switch to a supported browser to ... (194 characters truncated) ... , but don’t fret — let’s give it another shot.Try again Some privacy related extensions may cause issues on x.com. Please disable them and try again.', 'twitter_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594423_0e71e1e2-bf65-4233-b02e-ee634060573a.png', 'linkedin_url': 'https://in.linkedin.com/company/makemytrip.com?open=outside', 'linkedin_text': 'MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Informati ... (15970 characters truncated) ...  Agreement, Privacy Policy, and Cookie Policy. LinkedIn LinkedIn is better on the app Don’t have the app? Get it in the Microsoft Store. Open the app', 'linkedin_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png', 'pinterest_url': 'not_found', 'pinterest_text': 'not_applicable', 'pinterest_screenshot': 'not_applicable', 'x_url': 'not_found', 'x_text': 'not_applicable', 'x_screenshot': 'not_applicable', 'completed_at': '2025-07-27T05:33:43.318920Z', 'policy_analysis_new_gemini_id': 36}
[2025-07-27 05:33:43,321: INFO/ForkPoolWorker-8] [generated in 0.00103s] {'reachability_percentage': 100.0, 'total_urls_processed': 13, 'returns_cancellation_exchange_url': 'not_found', 'returns_cancellation_exchange_text': 'not_applicable', 'returns_cancellation_exchange_screenshot': 'not_applicable', 'privacy_policy_url': 'not_found', 'privacy_policy_text': 'not_applicable', 'privacy_policy_screenshot': 'not_applicable', 'terms_and_condition_url': 'https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no', 'terms_and_condition_text': "MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare ( ... (2438 characters truncated) ... he right to deny the offer against such bookings or to cancel such bookings. For such cases, MakeMyTrip shall not refund the booking amount. Book Now", 'terms_and_condition_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png', 'shipping_delivery_url': 'not_found', 'shipping_delivery_text': 'not_applicable', 'shipping_delivery_screenshot': 'not_applicable', 'contact_us_url': 'https://www.makemytrip.com/support/contact-us.php', 'contact_us_text': "Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugr ... (2510 characters truncated) ... f the cheapest flight of your choice today while also enjoying the other available options for your travel needs with us. © 2025 MAKEMYTRIP PVT. LTD.", 'contact_us_screenshot': 'screenshot_failed', 'about_us_url': 'not_found', 'about_us_text': 'not_applicable', 'about_us_screenshot': 'not_applicable', 'instagram_url': 'https://www.instagram.com/makemytrip', 'instagram_text': 'text_extraction_failed', 'instagram_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png', 'youtube_url': 'not_found', 'youtube_text': 'not_applicable', 'youtube_screenshot': 'not_applicable', 'facebook_url': 'https://www.facebook.com/makemytrip', 'facebook_text': 'text_extraction_failed', 'facebook_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png', 'twitter_url': 'https://x.com/makemytrip', 'twitter_text': 'JavaScript is not available. We’ve detected that JavaScript is disabled in this browser. Please enable JavaScript or switch to a supported browser to ... (194 characters truncated) ... , but don’t fret — let’s give it another shot.Try again Some privacy related extensions may cause issues on x.com. Please disable them and try again.', 'twitter_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594423_0e71e1e2-bf65-4233-b02e-ee634060573a.png', 'linkedin_url': 'https://in.linkedin.com/company/makemytrip.com?open=outside', 'linkedin_text': 'MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Informati ... (15970 characters truncated) ...  Agreement, Privacy Policy, and Cookie Policy. LinkedIn LinkedIn is better on the app Don’t have the app? Get it in the Microsoft Store. Open the app', 'linkedin_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png', 'pinterest_url': 'not_found', 'pinterest_text': 'not_applicable', 'pinterest_screenshot': 'not_applicable', 'x_url': 'not_found', 'x_text': 'not_applicable', 'x_screenshot': 'not_applicable', 'completed_at': '2025-07-27T05:33:43.318920Z', 'policy_analysis_new_gemini_id': 36}
2025-07-27 05:33:43,342 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-27 05:33:43,342: INFO/ForkPoolWorker-8] COMMIT
[2025-07-27 05:33:43,359: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Analysis results saved to database
{
  "categories_saved": 13,
  "analysis_flow": "normal",
  "reachability_percentage": 100.0
}
[2025-07-27 05:33:43,360: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: Analysis results saved successfully
[2025-07-27 05:33:43,361: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:33:43,361: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📤 STEP 5: SENDING WEBHOOK NOTIFICATION
[2025-07-27 05:33:43,361: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ----------------------------------------
[2025-07-27 05:33:43,362: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting webhook preparation
{
  "website": "https://www.makemytrip.com/",
  "content_data_keys": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter",
    "returns_cancellation_exchange",
    "x",
    "pinterest",
    "shipping_delivery",
    "youtube",
    "about_us",
    "privacy_policy"
  ],
  "content_data_size": 13,
  "content_data_details": {
    "terms_and_condition": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "contact_us": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "instagram": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "facebook": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "linkedin": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "twitter": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "returns_cancellation_exchange": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "x": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "pinterest": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "shipping_delivery": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "youtube": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "about_us": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "privacy_policy": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    }
  }
}
[2025-07-27 05:33:43,363: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📱 Collected Twitter data for merging
{
  "category": "twitter",
  "has_url": true,
  "has_text": true,
  "has_screenshot": true
}
[2025-07-27 05:33:43,363: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📱 Collected X data for merging
{
  "category": "x",
  "has_url": true,
  "has_text": true,
  "has_screenshot": true
}
[2025-07-27 05:33:43,364: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🚫 Excluding About Us (AU) category from webhook payload
{
  "category": "about_us",
  "reason": "excluded_by_requirement"
}
[2025-07-27 05:33:43,365: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🔄 Merging Twitter and X data - prioritizing X data
{
  "twitter_url": "https://x.com/makemytrip",
  "x_url": "not_found",
  "selected_source": "X"
}
[2025-07-27 05:33:43,365: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Successfully merged Twitter/X data
{
  "merge_source": "X (prioritized over Twitter)",
  "final_category": "X",
  "url": "not_found"
}
[2025-07-27 05:33:43,366: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text truncated from 443 to 100 words for API compatibility
[2025-07-27 05:33:43,366: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text truncated from 445 to 100 words for API compatibility
[2025-07-27 05:33:43,367: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text truncated from 2339 to 100 words for API compatibility
[2025-07-27 05:33:43,367: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📤 Final webhook payload policy types
{
  "policy_types": [
    "TNC",
    "CU",
    "IG",
    "FB",
    "LI",
    "RAC",
    "PT",
    "SD",
    "YT",
    "PP",
    "X"
  ],
  "total_policies": 11,
  "excluded_categories": [
    "AU"
  ],
  "merged_categories": "TW+X -> X"
}
[2025-07-27 05:33:43,367: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📤 WEBHOOK REQUEST PREPARATION - Enhanced Policy Service
{
  "webhook_url": "https://bffapi.biztel.ai/api/policy/results",
  "method": "PATCH",
  "headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_configured": true,
  "api_key_length": 8,
  "content_type": "application/json",
  "payload_size_bytes": 3782,
  "payload_size_kb": 3.69,
  "policies_count": 11,
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379",
  "website": "https://www.makemytrip.com/",
  "org_id": 1,
  "timestamp": "2025-07-27T05:33:43.367654"
}
[2025-07-27 05:33:43,368: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📋 WEBHOOK PAYLOAD STRUCTURE - Enhanced Policy Service
{
  "payload_structure": {
    "website": "https://www.makemytrip.com/",
    "scrapeRequestUuid": "428220ca-60c7-4af6-ab0e-799e4ede7379",
    "createdDate": "2025-07-27T05:33:43.367610Z",
    "status": "COMPLETED",
    "org_id": 1,
    "policies_count": 11,
    "policy_types": [
      "TNC",
      "CU",
      "IG",
      "FB",
      "LI",
      "RAC",
      "PT",
      "SD",
      "YT",
      "PP",
      "X"
    ],
    "policy_details": [
      {
        "type": "TNC",
        "has_url": true,
        "has_screenshot": true,
        "has_text": true,
        "url_preview": "https://promos.makemytrip.com/mmt-north-bus-tncs-2...",
        "text_length": 605
      },
      {
        "type": "CU",
        "has_url": true,
        "has_screenshot": false,
        "has_text": true,
        "url_preview": "https://www.makemytrip.com/support/contact-us.php",
        "text_length": 695
      },
      {
        "type": "IG",
        "has_url": true,
        "has_screenshot": true,
        "has_text": false,
        "url_preview": "https://www.instagram.com/makemytrip",
        "text_length": 22
      },
      {
        "type": "FB",
        "has_url": true,
        "has_screenshot": true,
        "has_text": false,
        "url_preview": "https://www.facebook.com/makemytrip",
        "text_length": 22
      },
      {
        "type": "LI",
        "has_url": true,
        "has_screenshot": true,
        "has_text": true,
        "url_preview": "https://in.linkedin.com/company/makemytrip.com?ope...",
        "text_length": 688
      },
      {
        "type": "RAC",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "PT",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "SD",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "YT",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "PP",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "X",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      }
    ]
  },
  "full_payload": {
    "website": "https://www.makemytrip.com/",
    "scrapeRequestUuid": "428220ca-60c7-4af6-ab0e-799e4ede7379",
    "createdDate": "2025-07-27T05:33:43.367610Z",
    "status": "COMPLETED",
    "policies": [
      {
        "type": "TNC",
        "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png",
        "text": "MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare (Pre-Tax Amount). The offer is not applicable on Government/ RTC buses. The offer will be applicable on booking amount exclusive of convenience fee, Insurance, Zero Cancellation Protection, Taxes & Ancillaries fee. The offer is not applicable on payments made through My Wallet (MakeMyTrip Wallet - bonus amount) and Gift card. Maximum discount limit: Rs. 300 Min booking price should be Rs. 200 How do you get it? To avail the offer customer must enter the"
      },
      {
        "type": "CU",
        "url": "https://www.makemytrip.com/support/contact-us.php",
        "imglink": "screenshot_failed",
        "text": "Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugram, Haryana 122002,India Fixed Line:(0124) 4628747 (0124) 5045105 Click for Directions Map Click here for our Retail Office Locations Existing Bookings For E-Tickets/Cancellations/Refund Status,please visit the My Trips section on App or Website For any other assistance, please contact our Customer Support service. For Flights / Hotels / Holidays(0124) 462 8747(0124) 5045105For Bus(0124) 4628765/(0124) 5045118 From all major operatorsFor Trains(For PNR enquiry & current status SMS to 139 : PNR <10 digit PNR number>)(For making your bookings"
      },
      {
        "type": "IG",
        "url": "https://www.instagram.com/makemytrip",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png",
        "text": "text_extraction_failed"
      },
      {
        "type": "FB",
        "url": "https://www.facebook.com/makemytrip",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png",
        "text": "text_extraction_failed"
      },
      {
        "type": "LI",
        "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png",
        "text": "MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Information and Internet Gurgaon, Harayana 918,628 followers See jobs Follow View all 6,884 employees Report this company Overview Jobs Life About us With our three powerhouse brands\u2014MakeMyTrip, Goibibo, and Redbus\u2014we are proud pioneers of online travel in India. We empower millions of travelers with easy and instant travel solutions, offering a wide range of services, including flights, hotels, homestays, holiday packages, cabs, buses, and trains. Our recent expansions include successful corporate travel solutions, a travel suite for agents,"
      },
      {
        "type": "RAC",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "PT",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "SD",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "YT",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "PP",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "X",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      }
    ],
    "org_id": 1
  }
}
[2025-07-27 05:33:43,368: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🚀 WEBHOOK REQUEST SENDING - Enhanced Policy Service
{
  "url": "https://bffapi.biztel.ai/api/policy/results",
  "method": "PATCH",
  "timeout": 30,
  "request_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "payload_size_kb": 3.69,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/policy/results' -H 'X-API-KEY: 12345678' -H 'Content-Type: application/json' -d '{\"website\": \"https://www.makemytrip.com/\", \"scrapeRequestUuid\": \"428220ca-60c7-4af6-ab0e-799e4ede7379\", \"createdDate\": \"2025-07-27T05:33:43.367610Z\", \"status\": \"COMPLETED\", \"policies\": [{\"type\": \"TNC\", \"url\": \"https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png\", \"text\": \"MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare (Pre-Tax Amount). The offer is not applicable on Government/ RTC buses. The offer will be applicable on booking amount exclusive of convenience fee, Insurance, Zero Cancellation Protection, Taxes & Ancillaries fee. The offer is not applicable on payments made through My Wallet (MakeMyTrip Wallet - bonus amount) and Gift card. Maximum discount limit: Rs. 300 Min booking price should be Rs. 200 How do you get it? To avail the offer customer must enter the\"}, {\"type\": \"CU\", \"url\": \"https://www.makemytrip.com/support/contact-us.php\", \"imglink\": \"screenshot_failed\", \"text\": \"Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugram, Haryana 122002,India Fixed Line:(0124) 4628747 (0124) 5045105 Click for Directions Map Click here for our Retail Office Locations Existing Bookings For E-Tickets/Cancellations/Refund Status,please visit the My Trips section on App or Website For any other assistance, please contact our Customer Support service. For Flights / Hotels / Holidays(0124) 462 8747(0124) 5045105For Bus(0124) 4628765/(0124) 5045118 From all major operatorsFor Trains(For PNR enquiry & current status SMS to 139 : PNR <10 digit PNR number>)(For making your bookings\"}, {\"type\": \"IG\", \"url\": \"https://www.instagram.com/makemytrip\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png\", \"text\": \"text_extraction_failed\"}, {\"type\": \"FB\", \"url\": \"https://www.facebook.com/makemytrip\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png\", \"text\": \"text_extraction_failed\"}, {\"type\": \"LI\", \"url\": \"https://in.linkedin.com/company/makemytrip.com?open=outside\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png\", \"text\": \"MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Information and Internet Gurgaon, Harayana 918,628 followers See jobs Follow View all 6,884 employees Report this company Overview Jobs Life About us With our three powerhouse brands\\u2014MakeMyTrip, Goibibo, and Redbus\\u2014we are proud pioneers of online travel in India. We empower millions of travelers with easy and instant travel solutions, offering a wide range of services, including flights, hotels, homestays, holiday packages, cabs, buses, and trains. Our recent expansions include successful corporate travel solutions, a travel suite for agents,\"}, {\"type\": \"RAC\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"PT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"SD\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"YT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"PP\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"X\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}], \"org_id\": 1}'",
  "timestamp": "2025-07-27T05:33:43.368482"
}
[2025-07-27 05:33:43,369: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): bffapi.biztel.ai:443
[2025-07-27 05:33:43,562: DEBUG/ForkPoolWorker-8] https://bffapi.biztel.ai:443 "PATCH /api/policy/results HTTP/1.1" 200 None
[2025-07-27 05:33:43,563: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📥 WEBHOOK RESPONSE RECEIVED - Enhanced Policy Service
{
  "status_code": 200,
  "success": true,
  "response_text": "{\"website\":\"https://www.makemytrip.com/\",\"createdDate\":\"2025-07-04T16:57:11.000+00:00\",\"scrapeRequestUuid\":\"428220ca-60c7-4af6-ab0e-799e4ede7379\",\"status\":\"COMPLETED\",\"policies\":[{\"type\":\"Terms\",\"url\":\"https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594243_39e69007-5cef-4c19-9073-46be32c3b918.png\",\"text\":\"MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare (Pre-Tax Amount). The offer is not applicable on Government/ RTC buses. The offer will be applicable on booking amount exclusive of convenience fee, Insurance, Zero Cancellation Protection, Taxes & Ancillaries fee. The offer is not applicable on payments made through My Wallet (MakeMyTrip Wallet - bonus amount) and Gift card. Maximum discount limit: Rs. 300 Min booking price should be Rs. 200 How do you get it? To avail the offer customer must enter the\"},{\"type\":\"Contact_page\",\"url\":\"https://www.makemytrip.com/support/contact-us.php\",\"imglink\":\"screenshot_failed\",\"text\":\"Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugram, Haryana 122002,India Fixed Line:(0124) 4628747 (0124) 5045105 Click for Directions Map Click here for our Retail Office Locations Existing Bookings For E-Tickets/Cancellations/Refund Status,please visit the My Trips section on App or Website For any other assistance, please contact our Customer Support service. For Flights / Hotels / Holidays(0124) 462 8747(0124) 5045105For Bus(0124) 4628765/(0124) 5045118 From all major operatorsFor Trains(For PNR enquiry & current status SMS to 139 : PNR <10 digit PNR number>)(For making your bookings\"},{\"type\":\"Instagram\",\"url\":\"https://www.instagram.com/makemytrip\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594324_34aefb9f-c662-4507-84dc-5cfa462efc78.png\",\"text\":\"text_extraction_failed\"},{\"type\":\"Facebook\",\"url\":\"https://www.facebook.com/makemytrip\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594391_2669f908-150c-4c7c-8ddc-a9f3e5498a65.png\",\"text\":\"text_extraction_failed\"},{\"type\":\"Linkedln\",\"url\":\"https://in.linkedin.com/company/makemytrip.com?open=outside\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594408_59b32c1c-18c3-47e2-b7c0-74b9566613ad.png\",\"text\":\"MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Information and Internet Gurgaon, Harayana 918,628 followers See jobs Follow View all 6,884 employees Report this company Overview Jobs Life About us With our three powerhouse brands\u2014MakeMyTrip, Goibibo, and Redbus\u2014we are proud pioneers of online travel in India. We empower millions of travelers with easy and instant travel solutions, offering a wide range of services, including flights, hotels, homestays, holiday packages, cabs, buses, and trains. Our recent expansions include successful corporate travel solutions, a travel suite for agents,\"},{\"type\":\"Refund_cancellation_policy\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Pinterest\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Shipping_delivery_policy\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Youtube\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Privacy_policy\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"X\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"}],\"registeredEntity\":null,\"processingStatus\":\"INITIATED\",\"org_id\":2}",
  "response_headers": {
    "Server": "nginx/1.22.1",
    "Date": "Sun, 27 Jul 2025 05:33:45 GMT",
    "Content-Type": "application/json",
    "Transfer-Encoding": "chunked",
    "Connection": "keep-alive",
    "Vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "0",
    "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate",
    "Pragma": "no-cache",
    "Expires": "0",
    "X-Frame-Options": "DENY"
  },
  "response_size_bytes": 3835,
  "response_size_kb": 3.75,
  "url": "https://bffapi.biztel.ai/api/policy/results",
  "payload_size_kb": 3.69,
  "request_duration_ms": 194.07,
  "timestamp": "2025-07-27T05:33:43.563324"
}
[2025-07-27 05:33:43,563: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ WEBHOOK SUCCESS - Enhanced Policy Service
{
  "status_code": 200,
  "response": "{\"website\":\"https://www.makemytrip.com/\",\"createdDate\":\"2025-07-04T16:57:11.000+00:00\",\"scrapeRequestUuid\":\"428220ca-60c7-4af6-ab0e-799e4ede7379\",\"status\":\"COMPLETED\",\"policies\":[{\"type\":\"Terms\",\"url\"",
  "policies_sent": 11,
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379"
}
[2025-07-27 05:33:43,564: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: Webhook notification sent
[2025-07-27 05:33:43,564: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:33:43,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📊 EXTRACTION SUMMARY:
[2025-07-27 05:33:43,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📋 Total categories: 13
[2025-07-27 05:33:43,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📝 Successful text extractions: 4
[2025-07-27 05:33:43,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📸 Successful screenshots: 5
[2025-07-27 05:33:43,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    🔗 Categories with URLs: 6
[2025-07-27 05:33:43,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    ⏱️ Processing time: 210.85s
[2025-07-27 05:33:43,566: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:33:43,566: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🎉 ENHANCED POLICY ANALYSIS COMPLETED SUCCESSFULLY!
[2025-07-27 05:33:43,566: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:33:43,566: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Enhanced policy analysis completed successfully
{
  "status": "COMPLETED",
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379",
  "website": "https://www.makemytrip.com/",
  "analysis_flow": "normal",
  "reachability_percentage": 100.0,
  "categories_processed": 13,
  "processing_time": 210.85451698303223,
  "webhook_sent": true,
  "unified_classification": true
}
[2025-07-27 05:33:43,567: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: Enhanced Policy Analysis task completed in 211.09 seconds with status: COMPLETED
[2025-07-27 05:33:43,568: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: 
[2025-07-27 05:33:43,568: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: 📤 SENDING POLICY RESULTS WEBHOOK
[2025-07-27 05:33:43,568: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: ==================================================
[2025-07-27 05:33:43,568: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: ✅ Sending successful policy analysis webhook
[2025-07-27 05:33:43,568: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: ✅ SUCCESS: Policy results webhook sent successfully
[2025-07-27 05:33:43,569: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][995][75aea3ff-1147-44d9-ab97-87d4a9bb5f30] INFO: ==================================================
[2025-07-27 05:33:43,569: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][process_cleanup][NO_REF] INFO: Running Celery task cleanup
[2025-07-27 05:33:43,574: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][process_cleanup][NO_REF] DEBUG: No Playwright processes found to clean up
[2025-07-27 05:33:43,575: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][process_cleanup][NO_REF] WARNING: Could not clean up async tasks: There is no current event loop in thread 'MainThread'.
[2025-07-27 05:33:43,575: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][process_cleanup][NO_REF] INFO: Celery task cleanup completed
[2025-07-27 05:33:43,578: WARNING/ForkPoolWorker-8] [2025-07-27 05:33:43][process_cleanup][NO_REF] DEBUG: No Playwright processes found to clean up
[2025-07-27 05:33:43,581: INFO/ForkPoolWorker-8] Task process_policy_analysis_enhanced[dd952dfc-0991-417f-9ddb-a2c77dd14f81] succeeded in 193.52903028100002s: {'status': 'COMPLETED', 'task_id': 'dd952dfc-0991-417f-9ddb-a2c77dd14f81', 'website': 'makemytrip.com', 'scrape_request_ref_id': '428220ca-60c7-4af6-ab0e-799e4ede7379', 'analysis_id': 995, 'execution_time': 211.09045672416687, 'analysis_flow': 'normal', 'reachability_percentage': 100.0, 'categories_processed': 13, 'webhook_sent': True}
[2025-07-27 05:38:52,411: INFO/MainProcess] Task process_policy_analysis_enhanced[18f1a148-ca28-4fb6-a396-eacdd37e7093] received
[2025-07-27 05:38:52,412: DEBUG/MainProcess] TaskPool: Apply <function fast_trace_task at 0x7fc8bfdd7b00> (args:('process_policy_analysis_enhanced', '18f1a148-ca28-4fb6-a396-eacdd37e7093', {'lang': 'py', 'task': 'process_policy_analysis_enhanced', 'id': '18f1a148-ca28-4fb6-a396-eacdd37e7093', 'shadow': None, 'eta': None, 'expires': None, 'group': None, 'group_index': None, 'retries': 0, 'timelimit': [2400, 2100], 'root_id': '18f1a148-ca28-4fb6-a396-eacdd37e7093', 'parent_id': None, 'argsrepr': '(996,)', 'kwargsrepr': '{}', 'origin': 'gen641@LAPTOP-CCUBTI4C', 'ignore_result': False, 'replaced_task_nesting': 0, 'stamped_headers': None, 'stamps': {}, 'properties': {'correlation_id': '18f1a148-ca28-4fb6-a396-eacdd37e7093', 'reply_to': 'ec05bfe0-6542-3285-8da7-d92bddcdf082', 'delivery_mode': 2, 'delivery_info': {'exchange': '', 'routing_key': 'policy_queue'}, 'priority': 0, 'body_encoding': 'base64', 'delivery_tag': '128154af-2702-461f-9ceb-39fa15a3dfb1'}, 'reply_to': 'ec05bfe0-6542-3285-8da7-d92bddcdf082', 'correlation_id': '18f1a148-ca28-4fb6-a396-eacdd37e7093', 'hostname': 'celery@LAPTOP-CCUBTI4C', 'delivery_info':... kwargs:{})
2025-07-27 05:38:52,423 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:38:52,423: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:38:52,425 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
[2025-07-27 05:38:52,425: INFO/ForkPoolWorker-8] SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = %(pk_1)s
2025-07-27 05:38:52,426 INFO sqlalchemy.engine.Engine [cached since 475.9s ago] {'pk_1': 996}
[2025-07-27 05:38:52,426: INFO/ForkPoolWorker-8] [cached since 475.9s ago] {'pk_1': 996}
2025-07-27 05:38:52,437 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-27 05:38:52,437: INFO/ForkPoolWorker-8] ROLLBACK
[2025-07-27 05:38:52,453: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][process_cleanup][NO_REF] DEBUG: Exit cleanup registered
[2025-07-27 05:38:52,453: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: Starting enhanced policy analysis task for analysis_id: 996
2025-07-27 05:38:52,462 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:38:52,462: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:38:52,463 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
[2025-07-27 05:38:52,463: INFO/ForkPoolWorker-8] SELECT policy_analysis_new_gemini.id AS policy_analysis_new_gemini_id, policy_analysis_new_gemini.website AS policy_analysis_new_gemini_website, policy_analysis_new_gemini.scrape_request_ref_id AS policy_analysis_new_gemini_scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used AS policy_analysis_new_gemini_analysis_flow_used, policy_analysis_new_gemini.reachability_percentage AS policy_analysis_new_gemini_reachability_percentage, policy_analysis_new_gemini.total_urls_processed AS policy_analysis_new_gemini_total_urls_processed, policy_analysis_new_gemini.home_page_url AS policy_analysis_new_gemini_home_page_url, policy_analysis_new_gemini.home_page_text AS policy_analysis_new_gemini_home_page_text, policy_analysis_new_gemini.home_page_screenshot AS policy_analysis_new_gemini_home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url AS policy_analysis_new_gemini_returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text AS policy_analysis_new_gemini_returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot AS policy_analysis_new_gemini_returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url AS policy_analysis_new_gemini_privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text AS policy_analysis_new_gemini_privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot AS policy_analysis_new_gemini_privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url AS policy_analysis_new_gemini_terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text AS policy_analysis_new_gemini_terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot AS policy_analysis_new_gemini_terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url AS policy_analysis_new_gemini_shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text AS policy_analysis_new_gemini_shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot AS policy_analysis_new_gemini_shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url AS policy_analysis_new_gemini_contact_us_url, policy_analysis_new_gemini.contact_us_text AS policy_analysis_new_gemini_contact_us_text, policy_analysis_new_gemini.contact_us_screenshot AS policy_analysis_new_gemini_contact_us_screenshot, policy_analysis_new_gemini.about_us_url AS policy_analysis_new_gemini_about_us_url, policy_analysis_new_gemini.about_us_text AS policy_analysis_new_gemini_about_us_text, policy_analysis_new_gemini.about_us_screenshot AS policy_analysis_new_gemini_about_us_screenshot, policy_analysis_new_gemini.instagram_url AS policy_analysis_new_gemini_instagram_url, policy_analysis_new_gemini.instagram_text AS policy_analysis_new_gemini_instagram_text, policy_analysis_new_gemini.instagram_screenshot AS policy_analysis_new_gemini_instagram_screenshot, policy_analysis_new_gemini.youtube_url AS policy_analysis_new_gemini_youtube_url, policy_analysis_new_gemini.youtube_text AS policy_analysis_new_gemini_youtube_text, policy_analysis_new_gemini.youtube_screenshot AS policy_analysis_new_gemini_youtube_screenshot, policy_analysis_new_gemini.facebook_url AS policy_analysis_new_gemini_facebook_url, policy_analysis_new_gemini.facebook_text AS policy_analysis_new_gemini_facebook_text, policy_analysis_new_gemini.facebook_screenshot AS policy_analysis_new_gemini_facebook_screenshot, policy_analysis_new_gemini.twitter_url AS policy_analysis_new_gemini_twitter_url, policy_analysis_new_gemini.twitter_text AS policy_analysis_new_gemini_twitter_text, policy_analysis_new_gemini.twitter_screenshot AS policy_analysis_new_gemini_twitter_screenshot, policy_analysis_new_gemini.linkedin_url AS policy_analysis_new_gemini_linkedin_url, policy_analysis_new_gemini.linkedin_text AS policy_analysis_new_gemini_linkedin_text, policy_analysis_new_gemini.linkedin_screenshot AS policy_analysis_new_gemini_linkedin_screenshot, policy_analysis_new_gemini.pinterest_url AS policy_analysis_new_gemini_pinterest_url, policy_analysis_new_gemini.pinterest_text AS policy_analysis_new_gemini_pinterest_text, policy_analysis_new_gemini.pinterest_screenshot AS policy_analysis_new_gemini_pinterest_screenshot, policy_analysis_new_gemini.x_url AS policy_analysis_new_gemini_x_url, policy_analysis_new_gemini.x_text AS policy_analysis_new_gemini_x_text, policy_analysis_new_gemini.x_screenshot AS policy_analysis_new_gemini_x_screenshot, policy_analysis_new_gemini.result_status AS policy_analysis_new_gemini_result_status, policy_analysis_new_gemini.created_at AS policy_analysis_new_gemini_created_at, policy_analysis_new_gemini.started_at AS policy_analysis_new_gemini_started_at, policy_analysis_new_gemini.completed_at AS policy_analysis_new_gemini_completed_at, policy_analysis_new_gemini.failed_at AS policy_analysis_new_gemini_failed_at, policy_analysis_new_gemini.last_updated AS policy_analysis_new_gemini_last_updated, policy_analysis_new_gemini.error_message AS policy_analysis_new_gemini_error_message, policy_analysis_new_gemini.details AS policy_analysis_new_gemini_details, policy_analysis_new_gemini.org_id AS policy_analysis_new_gemini_org_id, policy_analysis_new_gemini.processing_status AS policy_analysis_new_gemini_processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.id = %(pk_1)s
2025-07-27 05:38:52,464 INFO sqlalchemy.engine.Engine [cached since 475.8s ago] {'pk_1': 996}
[2025-07-27 05:38:52,464: INFO/ForkPoolWorker-8] [cached since 475.8s ago] {'pk_1': 996}
[2025-07-27 05:38:52,475: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: Found PolicyAnalysisNew record: https://makemytrip.com, ref_id: 428220ca-60c7-4af6-ab0e-799e4ede7379
2025-07-27 05:38:52,476 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET started_at=%(started_at)s, processing_status=%(processing_status)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
[2025-07-27 05:38:52,476: INFO/ForkPoolWorker-8] UPDATE policy_analysis_new_gemini SET started_at=%(started_at)s, processing_status=%(processing_status)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
2025-07-27 05:38:52,477 INFO sqlalchemy.engine.Engine [cached since 475.8s ago] {'started_at': '2025-07-27T05:38:52.476031Z', 'processing_status': 'PROCESSING', 'policy_analysis_new_gemini_id': 996}
[2025-07-27 05:38:52,477: INFO/ForkPoolWorker-8] [cached since 475.8s ago] {'started_at': '2025-07-27T05:38:52.476031Z', 'processing_status': 'PROCESSING', 'policy_analysis_new_gemini_id': 996}
2025-07-27 05:38:52,486 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-27 05:38:52,486: INFO/ForkPoolWorker-8] COMMIT
[2025-07-27 05:38:52,510: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: Updated PolicyAnalysisNew status to PROCESSING
[2025-07-27 05:38:52,511: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: Initializing Enhanced Policy Analysis service
[2025-07-27 05:38:52,512: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Enhanced Policy Analysis Service initialized
{
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379",
  "org_id": "default",
  "required_categories": 6,
  "social_media_categories": 7
}
[2025-07-27 05:38:52,512: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: Running Enhanced Policy Analysis with conditional popup handling
[2025-07-27 05:38:52,513: DEBUG/ForkPoolWorker-8] Using selector: EpollSelector
[2025-07-27 05:38:52,515: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:38:52,516: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🚀 ENHANCED POLICY ANALYSIS STARTED (UNIFIED APPROACH)
[2025-07-27 05:38:52,516: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:38:52,517: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📋 Request ID: 428220ca-60c7-4af6-ab0e-799e4ede7379
[2025-07-27 05:38:52,517: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🏢 Organization: default
[2025-07-27 05:38:52,518: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:38:52,518: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:38:52,518: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📋 STEP 1: RETRIEVING URLs FROM DATABASE
[2025-07-27 05:38:52,519: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: --------------------------------------------------
2025-07-27 05:38:52,527 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:38:52,527: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:38:52,528 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
[2025-07-27 05:38:52,528: INFO/ForkPoolWorker-8] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-27 05:38:52,529 INFO sqlalchemy.engine.Engine [cached since 475.8s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
[2025-07-27 05:38:52,529: INFO/ForkPoolWorker-8] [cached since 475.8s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
2025-07-27 05:38:52,540 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
[2025-07-27 05:38:52,540: INFO/ForkPoolWorker-8] SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-27 05:38:52,541 INFO sqlalchemy.engine.Engine [cached since 475.8s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
[2025-07-27 05:38:52,541: INFO/ForkPoolWorker-8] [cached since 475.8s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
2025-07-27 05:38:52,553 INFO sqlalchemy.engine.Engine ROLLBACK
[2025-07-27 05:38:52,553: INFO/ForkPoolWorker-8] ROLLBACK
[2025-07-27 05:38:52,569: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: URLs successfully retrieved
[2025-07-27 05:38:52,569: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Website: https://www.makemytrip.com/
[2025-07-27 05:38:52,570: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Depth 1 URLs: 172
[2025-07-27 05:38:52,570: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Depth 2 URLs: 0
[2025-07-27 05:38:52,571: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Total URLs: 172
[2025-07-27 05:38:52,571: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:38:52,572: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🏷️ STEP 2: CLASSIFYING URLs (UNIFIED SOFT → HARD)
[2025-07-27 05:38:52,572: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: --------------------------------------------------
[2025-07-27 05:38:52,573: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting URL classification using proper service methods
[2025-07-27 05:38:52,573: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting soft classification
{
  "website": "https://www.makemytrip.com/",
  "urls_depth_1_count": 172,
  "urls_depth_2_count": 0
}
[2025-07-27 05:38:52,576: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 172,
  "sample_urls": [
    "https://www.makemytrip.com/",
    "https://www.makemytrip.com/promos/jk-bank-offer-190225.html?detail_image=no",
    "https://www.facebook.com/makemytrip"
  ]
}
[2025-07-27 05:38:52,576: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://www.makemytrip.com/",
  "total_urls": 172
}
[2025-07-27 05:38:52,579: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=990, available_for_urls=87745
[2025-07-27 05:38:52,582: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][test-analysis][NO_REF] INFO: Total URL tokens: 4766, Available: 87745
[2025-07-27 05:38:52,582: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-27 05:38:52,583: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 172,
  "final_url_count": 172,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 990,
  "url_tokens": 4766,
  "final_total_tokens": 6021,
  "token_limit": 90000,
  "remaining_tokens": 83979
}
[2025-07-27 05:38:52,585: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 6869,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.009201288223266602
}
[2025-07-27 05:38:52,666: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-27 05:38:52,666: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:52][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-27 05:38:52,669: DEBUG/ForkPoolWorker-8] Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'user', 'content': '  **Context*\n    You are an experienced Business Analayst who deals with the information around companies and its websites and therefore you are very well versed with the URLs.\n    You have all the capability to just look at the URL and decide on the categry of the URL, if its a\n        1. home page URL, OR\n        2. about us page URLs OR\n        3. contact us or customer support URLs OR\n        4. terms and condition URLs OR\n        5. privacy policy URLs OR\n        6. return, refund and cancellation URLs OR\n        7. products URLs\n        8. service URLs\n        9. delivery or shipping policy related URLs OR\n        10. catalouge URLs\n        11. instagram URL page of the business  \n        12. facebook URL page of the business \n        13. youtube URL page of the business \n        14. twitter URL page of the business \n        15. linkedin URL page of the business \n        16. pinterest URL page of the business\n\n    You will be given a website name and dictionary of URLs for that websites, dictionary will contain keys as an integer index and values as URL correspnding to the integer index.\n    \n    * Information on website and its URLs starts *\n    website name --> https://www.makemytrip.com/\n    dictionary of URLs of the website named "dictionary_of_URLs" --> {0: \'https://www.makemytrip.com/\', 1: \'https://www.makemytrip.com/promos/jk-bank-offer-190225.html?detail_image=no\', 2: \'https://www.facebook.com/makemytrip\', 3: \'https://platforms.makemytrip.com/contents/00afdadb-be69-44b4-848e-66795bd4a078\', 4: \'https://www.makemytrip.com/forex?utm_source=MMT&amp;utm_medium=mmt_app&amp;utm_campaign=ForexBrandCampaign_chiclet&amp;showHeader=false\', 5: \'https://www.makemytrip.com/tripideas/top-staycation-in-around-mumbai-for-weekend\', 6: \'https://www.makemytrip.com/tripideas/relaxation-destinations\', 7: \'https://cabs.makemytrip.com/?tripType=AT\', 8: \'https://www.makemytrip.com/promos/aadhaar-link-tatkal-bookings.html?detail_image=no\', 9: \'https://promos.makemytrip.com/appfest/2x//desktop-Monsoon-Camp-130625.jpg?im=Resize=\', 10: \'https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no\', 11: \'https://www.makemytrip.com/promos/bob-nce-********.html?detail_image=no\', 12: \'https://www.makemytrip.com/cards/makemytrip-icici-bank-credit-card\', 13: \'https://www.makemytrip.com/promos/intl-indian-food-201023.html\', 14: \'https://platforms.makemytrip.com/contents/7300a7ec-9452-4c2b-b2aa-e8b2ebee7f12\', 15: \'https://www.makemytrip.com/tripideas\', 16: \'https://www.makemytrip.com/tripideas/places/dooars\', 17: \'https://www.makemytrip.com/international-flights\', 18: \'https://www.makemytrip.com/promos/au-bank-cc-offer-310325.html?detail_image=no\', 19: \'https://www.makemytrip.com/promos/student-if-fest-160625.html?detail_image=no\', 20: \'https://www.makemytrip.com/promos/etihad-airways-010725.html?detail_image=no\', 21: \'https://bookmyforex.com/?utm_source=makemytrip&utm_medium=referral&utm_campaign=gommt-landing&utm_id=gommt-landing\', 22: \'https://www.makemytrip.com/homestays\', 23: \'https://www.makemytrip.com/promos/ancillary-meals.html?detail_image=no\', 24: \'https://www.makemytrip.com/tripideas/places/guntur\', 25: \'https://www.makemytrip.com/bus-tickets\', 26: \'https://www.makemytrip.com/promos/idfc-nce-********.html?detail_image=no\', 27: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PO2PHMPFEECD4_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MjcyLGFkSXRlbUlkOzIyMzYzLDIyMTU5NzM3NQ==\', 28: \'https://www.makemytrip.com/hotels/hotel-listing?_uCurrency=INR&amp;checkin=date_7&amp;checkout=date_8&amp;city=SFALLLA&amp;country=IN&amp;locusId=SFALLLA&amp;locusType=storefront&amp;roomStayQualifier=2e0e&amp;searchText=All%20The%20Lalit%20Groups%20Properties%20in%20India&amp;type=storefront&amp;detail_image=no\', 29: \'https://www.makemytrip.com/promos/hsbc-emi-offers-apr22.html?detail_image=no\', 30: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PX5ESOLUJ8B5K_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NzQ0LGFkSXRlbUlkOzIwMzY5LDk0NzE1MDEzMQ==\', 31: \'https://www.makemytrip.com/how2go\', 32: \'https://in.linkedin.com/company/makemytrip.com?open=outside\', 33: \'https://promos.makemytrip.com/bus-train-pass-tncs-080425.html?detail_image=no\', 34: \'https://www.makemytrip.com/tripideas/places/shoghi\', 35: \'https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&amp;display_image=no\', 36: \'https://www.makemytrip.com/tripideas/places/tadoba\', 37: \'https://www.makemytrip.com/tripideas/adventure-destinations\', 38: \'https://www.makemytrip.com/tripideas/top-staycation-in-around-bangalore-for-weekend\', 39: \'https://www.makemytrip.com/promos/gt-suhana-safar-sale-070725.html?detail_image=no\', 40: \'https://www.makemytrip.com/promos/trains-trip-guarantee.html?popup=noShow&display_image=no\', 41: \'https://www.makemytrip.com/?ccde=in&lang=hin\', 42: \'https://www.makemytrip.com/promos/gt-bus-trip-assured-030624.html?detail_image=no\', 43: \'https://www.makemytrip.com/forex?utm_source=MMT&utm_medium=mmt_app&utm_campaign=ForexBrandCampaign_chiclet&showHeader=false\', 44: \'https://visa.makemytrip.com/landing\', 45: \'https://www.makemytrip.com/tripideas/beach-destinations\', 46: \'https://www.makemytrip.com/promos/axis-offer-150524.html?detail_image=no\', 47: \'https://www.makemytrip.com/tripideas/places/dhanaulti\', 48: \'https://www.makemytrip.com/tripideas/heritage-destinations\', 49: \'https://www.makemytrip.com/promos/mmt-group-travel.html?detail_image=no\', 50: \'https://tripmoney.makemytrip.com/ext/api/v1/partners/mmt/initOTU?product=insurance&utm_source=secondaryicon&utm_medium=mmtpwa&utm_campaign=ins_mmt_pwa\', 51: \'https://www.makemytrip.com/promos/taj-stays-300625.html?detail_image=no\', 52: \'https://www.makemytrip.com/hotels/hotel-details/?hotelId=201212131521547548&amp;mtkeys=defaultMtkey&amp;_uCurrency=INR&amp;checkin=date_7&amp;checkout=date_8&amp;city=CTRNG&amp;country=IN&amp;&amp;locusId=CTRNG&amp;locusType=city&amp;rank=3&amp;roomStayQualifier=2e0e&amp;detail_image=no\', 53: \'https://www.makemytrip.com/tripideas/places/vellore\', 54: \'https://platforms.makemytrip.com/contents/f51d1b5c-1ade-464f-ac92-f8b5d36b6a2b\', 55: \'https://www.makemytrip.com/hotels/hotel-listing?_uCurrency=INR&checkin=date_7&checkout=date_8&city=SFALLLA&country=IN&locusId=SFALLLA&locusType=storefront&roomStayQualifier=2e0e&searchText=All%20The%20Lalit%20Groups%20Properties%20in%20India&type=storefront&detail_image=no\', 56: \'https://www.makemytrip.com/promos/if-trip-120124.html?detail_image=no\', 57: \'https://brands.makemytrip.com/Kingston?showHeader=false&open=browser\', 58: \'https://cabs.makemytrip.com/\', 59: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PX5ESOLUJ8B5K_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NzQ0LGFkSXRlbUlkOzIwMzY5LDk0NzE1MDEzMQ==\', 60: \'https://visa.makemytrip.com/\', 61: \'https://www.makemytrip.com/promos/gt-suhana-safar-sale-070725.html\', 62: \'https://x.com/makemytrip\', 63: \'https://www.makemytrip.com/flights\', 64: \'https://promos.makemytrip.com/appfest/2x//cred-116x116-********.jpg?im=Resize=\', 65: \'https://www.makemytrip.com/tripideas/places/mandarmani\', 66: \'https://promos.makemytrip.com/appfest/2x//student-on-board-dt-********.jpg?im=Resize=\', 67: \'https://www.makemytrip.com/promos/virgin-atlantic-offer-100325.html?detail_image=no\', 68: \'https://www.makemytrip.com/promos/canara-bank-offers-220723.html?detail_image=no\', 69: \'https://promos.makemytrip.com/images//Desktop-HongKong-30Jun.jpg?im=Resize=\', 70: \'https://www.makemytrip.com/promos/egypt-air-sale-010725.html?detail_image=no\', 71: \'https://www.makemytrip.com/promos/rupay-salary-offer-310823.html?detail_image=no\', 72: \'https://www.makemytrip.com/promos/rbl-nce-********.html?detail_image=no\', 73: \'https://www.makemytrip.com/holidays-india\', 74: \'https://www.makemytrip.com/tripideas/places/sasan-gir\', 75: \'https://www.makemytrip.com/promos/dh-icici-********.html?detail_image=no\', 76: \'https://www.makemytrip.com/tripideas/weekend-getaways\', 77: \'https://www.makemytrip.com/hotels/hotel-listing/?_uCurrency=INR&amp;checkin=date_7&amp;checkout=date_8&amp;city=SFROSE&amp;country=IN&amp;locusId=SFROSE&amp;locusType=storefront&amp;roomStayQualifier=2e0e&amp;searchText=All%20Shangri&nbsp;La\', 78: \'https://www.makemytrip.com/tripideas/places/bhandardara\', 79: \'https://www.makemytrip.com/\', 80: \'https://www.makemytrip.com/promos/qatar-airways-040725.html?detail_image=no\', 81: \'https://www.makemytrip.com/promos/train-seat-availability-forecast-230625.html?detail_image=no\', 82: \'https://www.makemytrip.com/tripideas/places/yelagiri\', 83: \'https://promos.makemytrip.com/mmt-south-bus-tncs-200524.html?detail_image=no\', 84: \'https://insurance.makemytrip.com/ext/api/v1/partners/mmt/initOTU?product=insurance&utm_source=secondaryicon&utm_medium=mmtpwa&utm_campaign=ins_mmt_pwa\', 85: \'https://www.makemytrip.com/promos/indigo-new-flights-080223.html?detail_image=no\', 86: \'https://www.makemytrip.com/railways\', 87: \'https://promos.makemytrip.com/mmtblack-bus-terms-071024.html?detail_image=no\', 88: \'https://www.makemytrip.com/tripideas/places/vagamon\', 89: \'https://www.makemytrip.com/promos/bhim-upi-offer-100125.html?detail_image=no\', 90: \'https://www.makemytrip.com/tripideas/pilgrimage-destinations\', 91: \'https://www.makemytrip.com/promos/cgh-hotel-sale-220823.html?detail_image=no\', 92: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PJP9S85UO5BM0_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NTE1LGFkSXRlbUlkOzE5NjkzLDI5NTEzNjI1\', 93: \'https://www.makemytrip.com/?_uCurrency=INR\', 94: \'https://www.makemytrip.com/promos/visa-signature-credit-cards-offer-280325.html?detail_image=no\', 95: \'https://www.makemytrip.com/tripideas/hills-mountains-destinations\', 96: \'https://www.makemytrip.com/promos/dh-regional-sale-01082024.html?detail_image=no\', 97: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PV4JS2GF66CQL_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MzU0LGFkSXRlbUlkOzIyNTY2LDY1NTYzNTQxMg==\', 98: \'https://www.makemytrip.com/promos/gt-at-cab-ride-guarantee-300125.html\', 99: \'https://www.makemytrip.com/promos/kenya-airways-030725.html?detail_image=no\', 100: \'https://www.makemytrip.com/forex\', 101: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PO2PHMPFEECD4_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MjcyLGFkSXRlbUlkOzIyMzYzLDIyMTU5NzM3NQ==\', 102: \'https://www.makemytrip.com/promos/yes-nce-offers-01072022.html?detail_image=no\', 103: \'https://www.makemytrip.com/railways/PNR\', 104: \'https://www.makemytrip.com/tripideas/places/narkanda\', 105: \'https://www.makemytrip.com/promos/malaysia-airline-210525.html?detail_image=no\', 106: \'https://www.makemytrip.com/hotels\', 107: \'https://brands.makemytrip.com/flight/airasia?showHeader=false&open=browser\', 108: \'https://www.instagram.com/makemytrip\', 109: \'https://brands.makemytrip.com/CGHEarth?open=browser\', 110: \'https://www.makemytrip.com/travel-guidelines/?schedule=webview\', 111: \'https://www.makemytrip.com/promos/air-astana-sale-010725.html?detail_image=no\', 112: \'https://www.makemytrip.com/insurance\', 113: \'https://www.makemytrip.com/promos/oyofest-19072023.html?detail_image=no\', 114: \'https://www.makemytrip.com/promos/nepal-airlines-130625.html?detail_image=no\', 115: \'https://www.makemytrip.com/promos/ih-offer-100524.html?detail_image=no\', 116: \'https://www.makemytrip.com/tripideas/places/srisailam\', 117: \'https://www.makemytrip.com/promos/new-user-campaign.html?offer=df&detail_page=no\', 118: \'https://supportz.makemytrip.com/mweb/bookingSummary\', 119: \'https://www.makemytrip.com/tripideas/places/parwanoo\', 120: \'https://www.makemytrip.com/tripideas/places/pachmarhi\', 121: \'https://www.makemytrip.com/promos/if-icici-060224.html?detail_image=no\', 122: \'https://www.makemytrip.com/support/mysafety/mysafety.php\', 123: \'https://www.makemytrip.com/promos/sonar-bangla-hotel-offer-200224.html?detail_image=no\', 124: \'https://platforms.makemytrip.com/contents/07e30964-bcda-4c81-873f-fbb283b1845f\', 125: \'https://adengine.makemytrip.com/ext/user/track?type=VIEW&trackingId=PV4JS2GF66CQL_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs3MzU0LGFkSXRlbUlkOzIyNTY2LDY1NTYzNTQxMg==\', 126: \'https://cabs.makemytrip.com/self-drive\', 127: \'https://www.makemytrip.com/tripideas/places/saputara\', 128: \'https://www.makemytrip.com/promos/monsoon-stays-offer-020725.html?detail_image=no\', 129: \'https://platforms.makemytrip.com/contents/8db292f3-fd5a-448c-9f2a-58c78e10f56c\', 130: \'https://www.makemytrip.com/promos/cred-upi-flight-offer-250625.html?detail_image=no\', 131: \'https://www.makemytrip.com/?referrer=gcWrapper\', 132: \'https://www.makemytrip.com/promos/fern-hotels-sale-231023.html?detail_image=no\', 133: \'https://www.makemytrip.com/akam/13/pixel_41767149?a=dD02ODViNzk2ODNjNzFiM2IyOTRlZTEzOTgxMmI0MzQ1MjJmNzQxYzZhJmpzPW9mZg==\', 134: \'https://www.makemytrip.com/cabs\', 135: \'https://holidayz.makemytrip.com/holidays/india/package?id=55204&fromCity=New%20Delhi&pkgType=FIT&listingClassId=4299&depDate=2025-05-26&searchDate=2025-05-26&glp=true&room=2%2C0%2C0%2C0%2C%2C%2C&variantId=FLIGHT_1DB875FA&detail_image=no\', 136: \'https://www.makemytrip.com/tripideas/top-staycation-in-around-delhi-for-weekend\', 137: \'https://holidayz.makemytrip.com/holidays/international\', 138: \'https://promos.makemytrip.com/trains-mmtalways-051124.html?detail_image=no&tab=trains\', 139: \'https://promos.makemytrip.com/notification/xhdpi//Desktop-ApartmentSale-17Apr.jpg?im=Resize=\', 140: \'https://promos.makemytrip.com/bus-flash-deals-tncs-031024.html?detail_image=no\', 141: \'https://www.makemytrip.com/tripideas/places/araku-valley\', 142: \'https://www.makemytrip.com/tripideas/places/yercaud\', 143: \'https://www.makemytrip.com/support/contact-us.php\', 144: \'https://www.makemytrip.com/tripideas/places/udupi\', 145: \'https://brands.makemytrip.com/flight/cathay?open=browser&showHeader=false\', 146: \'https://www.makemytrip.com/hotels/hotel-details/?hotelId=201212131521547548&mtkeys=defaultMtkey&_uCurrency=INR&checkin=date_7&checkout=date_8&city=CTRNG&country=IN&&locusId=CTRNG&locusType=city&rank=3&roomStayQualifier=2e0e&detail_image=no\', 147: \'https://www.makemytrip.com/mice\', 148: \'https://www.makemytrip.com/promos/df-icici-********.html?detail_image=no\', 149: \'https://adorch.makemytrip.com/ext/user/track?type=view&trackingId=TU1UOzQ1NjgxMzUyMDAwMjAwMjgyNTIyNw==\', 150: \'https://platforms.makemytrip.com/contents/fb0b8518-346f-4c5d-b86e-bc520d8111cf\', 151: \'https://www.makemytrip.com/hotels/hotel-listing/?_uCurrency=INR&checkin=date_7&checkout=date_8&city=SFROSE&country=IN&locusId=SFROSE&locusType=storefront&roomStayQualifier=2e0e&searchText=All%20Shangri\', 152: \'https://www.makemytrip.com/promos/new-user-campaign.html?offer=df&amp;detail_page=no\', 153: \'https://www.makemytrip.com/promos/ama-stays-131024.html?detail_image=no\', 154: \'https://www.makemytrip.com/tripideas/places/dapoli\', 155: \'https://www.makemytrip.com/mbus\', 156: \'https://adorch.makemytrip.com/ext/user/track?type=click&trackingId=TU1UOzQ1NjgxMzUyMDAwMjAwMjgyNTIyNw==\', 157: \'https://www.makemytrip.com/promos/singapore-airlines-030625.html?detail_image=no\', 158: \'https://adengine.makemytrip.com/ext/user/track?type=CLICK&trackingId=PJP9S85UO5BM0_dHJhY2tlcixJTUcsbGluZUl0ZW1JZDs2NTE1LGFkSXRlbUlkOzE5NjkzLDI5NTEzNjI1\', 159: \'https://platforms.makemytrip.com/contents/3db1e6f2-270b-4783-8330-710d4950f6b0\', 160: \'https://www.makemytrip.com/tripideas/places/malvan\', 161: \'https://www.makemytrip.com/gift-cards\', 162: \'https://promos.makemytrip.com/appfest/2x//taj-116x116-03072025.jpg?im=Resize=\', 163: \'https://www.makemytrip.com/promos/au-offer-020124.html?detail_image=no\', 164: \'https://www.makemytrip.com/promos/federal-offer-050624.html?detail_image=no\', 165: \'https://promos.makemytrip.com/trains-mmtalways-051124.html?detail_image=no&amp;tab=trains\', 166: \'https://www.makemytrip.com/promos/top-rated-homestays-060725.html?detail_image=no\', 167: \'https://www.makemytrip.com/tripideas/places/dandeli\', 168: \'https://promos.makemytrip.com/mmt-travel-trends-report-apr24.pdf\', 169: \'https://holidayz.makemytrip.com/holidays/india/package?id=55204&amp;fromCity=New%20Delhi&amp;pkgType=FIT&amp;listingClassId=4299&amp;depDate=2025-05-26&amp;searchDate=2025-05-26&amp;glp=true&amp;room=2%2C0%2C0%2C0%2C%2C%2C&amp;variantId=FLIGHT_1DB875FA&amp;detail_image=no\', 170: \'https://www.makemytrip.com/promos/mmt-icici-cards.html\', 171: \'https://www.makemytrip.com/tripideas/places/vengurla\'}\n    "dictionary_of_URLs" will contain following information\n        a. key will be an integer\n        b. value will be the URL corresponding to this integer key\n    * Information on website and URLs ends *\n\n\n    * Tasks Starts *\n    You will have to categorize these website\'s URLs into the following categories.\n    [\'home_page\',\'about_us\',\'terms_and_condition\',\'returns_cancellation_exchange\',\'privacy_policy\',\'shipping_delivery\',\'contact_us\',\'products\',\'services\',\'catalogue\',\'instagram_page\',\'facebook_page\',\'youtube_page\',\'twitter_page\',\'linkedin_page\',\'pinterest_page\']\n    You have to extract the index corresponding to these categories.\n    You will have to output just index that correspond the correct URLs.\n\n\n    * Guidelines for the task *\n    1. Please go through all the key words mentioned in the URLs and think of a logical reason why it can be classified into these URLs.\n    2. home page is generally the landing page and also the shortest URL amongst all the URL.\n    3. about us page will have companies information and not founders or people information on it.\n    4. Cataloge URL will be the one having all categories of products and services at one place. Assign catalogue URL only whan you are very very sure about it.\n    5. Cataloge URL can be something like, "collections", "shop", "all products" , "all services" etc.\n    6. Extract only 5 random indexes of product and service pages. These index should have maximum possible products and services. Try not to extract URLs which are very specific to just one product and service.\n    7. URLs containing social media links for the business will mostly have respective name in the URL, like instagram, facebook, twitter, linkedin, youtube, pinterest\n    8. LinkedIn page should be of the company/business. It should not be of the founder/people.\n    9. Social media URLs should be of the company or the business. DO NOT output the founder page for facebook, youtube and instagram. \n    10. If you have slighest of chance that a URL can lie in any of these catgories, please assign it to that category.\n    11. Do not output an index which is not there in any key.\n\n    *** Important Note *** --> Please note that one same URL can go to 2 or more categories.\n\n    * Tasks Ends *\n\n    Output shoud strictly be in a json format containing these 16 keys.\n            1. \'home_page\':[], a list of integers which represent the URLs in "dictionary_of_URL", -->  Stick to one URL, multiple home page URLs are not required.\n            2. \'about_us\':[], a list of integers which represent the URLs in "dictionary_of_URL", -->  Stick to one URL, multiple home page URLs are not required.\n            3. \'terms_and_condition\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            4. \'returns_cancellation_exchange\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            5. \'privacy_policy\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            6. \'shipping_delivery\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            7. \'contact_us\':[], a list of integers which represent the URLs in "dictionary_of_URL"\n            8. \'products\':[], a list of integers with maximum 5 entries which represent the URLs in "dictionary_of_URL"\n            9. \'services\':[], a list of integers with maximum 5 entries which represent the URLs in "dictionary_of_URL"\n            10.\'catalogue\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            11.\'instagram_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            12.\'facebook_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            13.\'twitter_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            14.\'linkedin_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            15.\'youtube_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n            16.\'pinterest_page\':[], a list with just one integer which represent the URLs in "dictionary_of_URL"\n\n            Output strictly should not contain any thing apart from is json.\n            Strictly avoid any keywords or string outsisde this list.\n    '}], 'model': 'gpt-4o', 'max_tokens': 6000, 'seed': 0, 'temperature': 0, 'top_p': 0.95}}
[2025-07-27 05:38:52,670: DEBUG/ForkPoolWorker-8] Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
[2025-07-27 05:38:52,671: DEBUG/ForkPoolWorker-8] connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=5.0 socket_options=None
[2025-07-27 05:38:52,687: DEBUG/ForkPoolWorker-8] connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fc8a0d61310>
[2025-07-27 05:38:52,688: DEBUG/ForkPoolWorker-8] start_tls.started ssl_context=<ssl.SSLContext object at 0x7fc8a0cbdad0> server_hostname='api.openai.com' timeout=5.0
[2025-07-27 05:38:52,713: DEBUG/ForkPoolWorker-8] start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x7fc8a0d62720>
[2025-07-27 05:38:52,714: DEBUG/ForkPoolWorker-8] send_request_headers.started request=<Request [b'POST']>
[2025-07-27 05:38:52,715: DEBUG/ForkPoolWorker-8] send_request_headers.complete
[2025-07-27 05:38:52,715: DEBUG/ForkPoolWorker-8] send_request_body.started request=<Request [b'POST']>
[2025-07-27 05:38:52,716: DEBUG/ForkPoolWorker-8] send_request_body.complete
[2025-07-27 05:38:52,716: DEBUG/ForkPoolWorker-8] receive_response_headers.started request=<Request [b'POST']>
[2025-07-27 05:38:54,797: DEBUG/ForkPoolWorker-8] receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sun, 27 Jul 2025 05:38:56 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'biztelai'), (b'openai-processing-ms', b'1963'), (b'openai-project', b'proj_6PLYrbQOGO2ZQ8mi69HcOdP8'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'1967'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'450000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'444825'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'690ms'), (b'x-request-id', b'req_dde642ef75ebe50106de47b0e578fefb'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=pJu1fK8x30YtpIMl5nmfwhEKWXLgDYK11WIV1k9ikds-1753594736-1.0.1.1-VzK9nEcr5t4YuqNKT_eDPhRkUJJW8PnSe3szvqfvDXeDysWMlU_dxg2XTfnwoLjUHpLeQjcEyHEZb.fgc5igPye7iDwcXH6vPBYgVB.Y25Y; path=/; expires=Sun, 27-Jul-25 06:08:56 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=0EknT2YDh5HTHDINK2NH7U8AxdRUdGrXQer.zJDhuRw-1753594736037-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9659cb0e1a17ff6d-BOM'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
[2025-07-27 05:38:54,799: INFO/ForkPoolWorker-8] HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-27 05:38:54,800: DEBUG/ForkPoolWorker-8] receive_response_body.started request=<Request [b'POST']>
[2025-07-27 05:38:54,801: DEBUG/ForkPoolWorker-8] receive_response_body.complete
[2025-07-27 05:38:54,802: DEBUG/ForkPoolWorker-8] response_closed.started
[2025-07-27 05:38:54,802: DEBUG/ForkPoolWorker-8] response_closed.complete
[2025-07-27 05:38:54,803: DEBUG/ForkPoolWorker-8] HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers([('date', 'Sun, 27 Jul 2025 05:38:56 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('access-control-expose-headers', 'X-Request-ID'), ('openai-organization', 'biztelai'), ('openai-processing-ms', '1963'), ('openai-project', 'proj_6PLYrbQOGO2ZQ8mi69HcOdP8'), ('openai-version', '2020-10-01'), ('x-envoy-upstream-service-time', '1967'), ('x-ratelimit-limit-requests', '5000'), ('x-ratelimit-limit-tokens', '450000'), ('x-ratelimit-remaining-requests', '4999'), ('x-ratelimit-remaining-tokens', '444825'), ('x-ratelimit-reset-requests', '12ms'), ('x-ratelimit-reset-tokens', '690ms'), ('x-request-id', 'req_dde642ef75ebe50106de47b0e578fefb'), ('strict-transport-security', 'max-age=31536000; includeSubDomains; preload'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=pJu1fK8x30YtpIMl5nmfwhEKWXLgDYK11WIV1k9ikds-1753594736-1.0.1.1-VzK9nEcr5t4YuqNKT_eDPhRkUJJW8PnSe3szvqfvDXeDysWMlU_dxg2XTfnwoLjUHpLeQjcEyHEZb.fgc5igPye7iDwcXH6vPBYgVB.Y25Y; path=/; expires=Sun, 27-Jul-25 06:08:56 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('x-content-type-options', 'nosniff'), ('set-cookie', '_cfuvid=0EknT2YDh5HTHDINK2NH7U8AxdRUdGrXQer.zJDhuRw-1753594736037-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9659cb0e1a17ff6d-BOM'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
[2025-07-27 05:38:54,804: DEBUG/ForkPoolWorker-8] request_id: req_dde642ef75ebe50106de47b0e578fefb
[2025-07-27 05:38:54,805: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:54][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=152, prompt_tokens=6791, total_tokens=6943, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=6784))
[2025-07-27 05:38:55,806: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Soft classification response received
{
  "response_length": 475,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [],\n    \"terms_and_condition\": [10, 33, 87],\n    \"returns_cancellation_exchange\": [],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [],\n    \"contac..."
}
[2025-07-27 05:38:55,806: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://www.makemytrip.com/",
  "dictionary_1_count": 172,
  "total_chars": 13605,
  "urls_sample": [
    "https://www.makemytrip.com/",
    "https://www.makemytrip.com/promos/jk-bank-offer-190225.html?detail_image=no",
    "https://www.facebook.com/makemytrip"
  ]
}
[2025-07-27 05:38:55,806: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [],
    "terms_and_condition": [
      10,
      33,
      87
    ],
    "returns_cancellation_exchange": [],
    "privacy_policy": [],
    "shipping_delivery": [],
    "contact_us": [
      143
    ],
    "products": [
      12,
      22,
      25,
      63,
      106
    ],
    "services": [
      7,
      73,
      86,
      112,
      134
    ],
    "catalogue": [],
    "instagram_page": [
      108
    ],
    "facebook_page": [
      2
    ],
    "twitter_page": [
      62
    ],
    "linkedin_page": [
      32
    ],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-27 05:38:55,816: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][url_classification_428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://www.makemytrip.com/"
    ],
    "about_us": [],
    "terms_and_condition": [
      "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
      "https://promos.makemytrip.com/bus-train-pass-tncs-080425.html?detail_image=no",
      "https://promos.makemytrip.com/mmtblack-bus-terms-071024.html?detail_image=no"
    ],
    "returns_cancellation_exchange": [],
    "privacy_policy": [],
    "shipping_delivery": [],
    "contact_us": [
      "https://www.makemytrip.com/support/contact-us.php"
    ],
    "products": [
      "https://www.makemytrip.com/cards/makemytrip-icici-bank-credit-card",
      "https://www.makemytrip.com/homestays",
      "https://www.makemytrip.com/bus-tickets",
      "https://www.makemytrip.com/flights",
      "https://www.makemytrip.com/hotels"
    ],
    "services": [
      "https://cabs.makemytrip.com/?tripType=AT",
      "https://www.makemytrip.com/holidays-india",
      "https://www.makemytrip.com/railways",
      "https://www.makemytrip.com/insurance",
      "https://www.makemytrip.com/cabs"
    ],
    "catalogue": [],
    "instagram_page": [
      "https://www.instagram.com/makemytrip"
    ],
    "facebook_page": [
      "https://www.facebook.com/makemytrip"
    ],
    "twitter_page": [
      "https://x.com/makemytrip"
    ],
    "linkedin_page": [
      "https://in.linkedin.com/company/makemytrip.com?open=outside"
    ],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 20,
  "priority_urls_count": 15
}
[2025-07-27 05:38:55,817: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: URL classification completed
[2025-07-27 05:38:55,817: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📊 Categories found: 16
[2025-07-27 05:38:55,817: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📋 Categories: home_page, about_us, terms_and_condition, returns_cancellation_exchange, privacy_policy, shipping_delivery, contact_us, products, services, catalogue, instagram_page, facebook_page, twitter_page, linkedin_page, youtube_page, pinterest_page
[2025-07-27 05:38:55,817: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
🔀 STEP 3: DETERMINING PROCESSING FLOW
--------------------------------------------------
[2025-07-27 05:38:55,818: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ NORMAL FLOW SELECTED: All URLs are reachable
{
  "backup_needed": false,
  "trigger_reason": "UNREACHABLE_VIA_TOOL_IS_EMPTY",
  "unreachable_via_tool_count": 0,
  "flow_decision": "NORMAL_FLOW",
  "note": "Will use hard classification for policy + soft classification for social media"
}
[2025-07-27 05:38:55,818: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Reachability calculation completed
{
  "total_priority_urls": 4,
  "priority_urls_reachable": 4,
  "priority_urls_not_reachable": 0,
  "reachability_percentage": 100.0
}
[2025-07-27 05:38:55,818: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ DECISION: Using NORMAL FLOW (HARD CLASSIFICATION)
{
  "reason": "All URLs verified as reachable",
  "method": "Hard classification for policy, soft for social media",
  "reachability": "100.00%"
}
[2025-07-27 05:38:55,818: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Using hard classification results (social media already merged by URL classification service)
[2025-07-27 05:38:55,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Processing hard classification flow
[2025-07-27 05:38:55,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🎯 Starting representative URL selection with priority order implementation
{
  "total_categories": 16,
  "categories_with_urls": 9
}
[2025-07-27 05:38:55,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Priority selection: terms_and_condition -> terms_and_condition
{
  "selected_url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "total_urls_in_category": 3,
  "urls_skipped": 2,
  "priority_rank": 1
}
[2025-07-27 05:38:55,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Priority selection: contact_us -> contact_us
{
  "selected_url": "https://www.makemytrip.com/support/contact-us.php",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "priority_rank": 4
}
[2025-07-27 05:38:55,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: instagram_page -> instagram
{
  "selected_url": "https://www.instagram.com/makemytrip",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "instagram_page",
  "db_category": "instagram"
}
[2025-07-27 05:38:55,819: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: facebook_page -> facebook
{
  "selected_url": "https://www.facebook.com/makemytrip",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "facebook_page",
  "db_category": "facebook"
}
[2025-07-27 05:38:55,820: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: linkedin_page -> linkedin
{
  "selected_url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "linkedin_page",
  "db_category": "linkedin"
}
[2025-07-27 05:38:55,820: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Social media selection: twitter_page -> twitter
{
  "selected_url": "https://x.com/makemytrip",
  "total_urls_in_category": 1,
  "urls_skipped": 0,
  "category_type": "social_media",
  "classification_category": "twitter_page",
  "db_category": "twitter"
}
[2025-07-27 05:38:55,820: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🏁 Representative URL selection completed
{
  "selected_categories": 6,
  "categories": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter"
  ],
  "total_urls_selected": 6
}
[2025-07-27 05:38:55,820: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Representative URLs selected
{
  "categories_selected": 6,
  "categories": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter"
  ]
}
[2025-07-27 05:38:55,821: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🔄 Starting content extraction for all categories
{
  "total_categories": 6,
  "categories": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter"
  ]
}
[2025-07-27 05:38:55,821: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no"
}
[2025-07-27 05:38:55,821: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no"
}
[2025-07-27 05:38:55,821: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no"
}
[2025-07-27 05:38:55,821: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:38:55,822: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): promos.makemytrip.com:443
[2025-07-27 05:38:55,880: DEBUG/ForkPoolWorker-8] https://promos.makemytrip.com:443 "GET /mmt-north-bus-tncs-200524.html?detail_image=no HTTP/1.1" 200 4690
[2025-07-27 05:38:55,885: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "text_length": 2736,
  "method": "requests"
}
[2025-07-27 05:38:55,886: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:38:55,886: WARNING/ForkPoolWorker-8] [2025-07-27 05:38:55][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no: 3000ms
[2025-07-27 05:39:01,260: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no
[2025-07-27 05:39:02,306: INFO/ForkPoolWorker-8] Navigating to https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no
[2025-07-27 05:39:02,655: INFO/ForkPoolWorker-8] Waiting 3000ms for images to load...
[2025-07-27 05:39:05,731: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:39:05,947: INFO/ForkPoolWorker-8] Screenshot captured in 4.69s, size: 151038 bytes
[2025-07-27 05:39:06,261: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:39:06,272: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png"
}
[2025-07-27 05:39:06,274: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '151038'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '02d251b7-6aac-11f0-bd77-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:39:06,393: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:39:08 GMT'
    'Etag': '"0x8DDCCCFE878C818"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '89c40168-701e-006a-57b8-fe633c000000'
    'x-ms-client-request-id': '02d251b7-6aac-11f0-bd77-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:39:08 GMT'
[2025-07-27 05:39:06,395: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png"
}
[2025-07-27 05:39:06,397: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png"
}
[2025-07-27 05:39:06,398: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "text_length": 2736,
  "screenshot_status": "success"
}
[2025-07-27 05:39:06,399: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for terms_and_condition
{
  "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
  "text_length": 2736,
  "has_screenshot": true
}
[2025-07-27 05:39:06,400: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php"
}
[2025-07-27 05:39:06,401: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php"
}
[2025-07-27 05:39:06,402: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://www.makemytrip.com/support/contact-us.php"
}
[2025-07-27 05:39:06,403: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:39:06,405: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): www.makemytrip.com:443
[2025-07-27 05:39:07,797: DEBUG/ForkPoolWorker-8] https://www.makemytrip.com:443 "GET /support/contact-us.php HTTP/1.1" 200 25836
[2025-07-27 05:39:07,808: DEBUG/ForkPoolWorker-8] close.started
[2025-07-27 05:39:07,809: DEBUG/ForkPoolWorker-8] close.complete
[2025-07-27 05:39:07,816: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:07][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "text_length": 2808,
  "method": "requests"
}
[2025-07-27 05:39:07,816: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:07][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://www.makemytrip.com/support/contact-us.php: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:39:07,817: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:07][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://www.makemytrip.com/support/contact-us.php: 3000ms
[2025-07-27 05:39:16,849: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://www.makemytrip.com/support/contact-us.php
[2025-07-27 05:39:17,372: INFO/ForkPoolWorker-8] Navigating to https://www.makemytrip.com/support/contact-us.php
[2025-07-27 05:39:17,495: ERROR/ForkPoolWorker-8] Error capturing screenshot: Page.goto: net::ERR_HTTP2_PROTOCOL_ERROR at https://www.makemytrip.com/support/contact-us.php
Call log:
  - navigating to "https://www.makemytrip.com/support/contact-us.php", waiting until "domcontentloaded"

[2025-07-27 05:39:17,760: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:39:17,770: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Screenshot capture returned no data for https://www.makemytrip.com/support/contact-us.php
[2025-07-27 05:39:17,770: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "text_length": 2808,
  "screenshot_status": "failed"
}
[2025-07-27 05:39:17,770: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for contact_us
{
  "url": "https://www.makemytrip.com/support/contact-us.php",
  "text_length": 2808,
  "has_screenshot": false
}
[2025-07-27 05:39:17,770: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for instagram
{
  "url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:39:17,771: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for instagram
{
  "url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:39:17,771: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:39:17,771: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:17][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://www.instagram.com/makemytrip",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:39:17,772: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): www.instagram.com:443
[2025-07-27 05:39:17,942: DEBUG/ForkPoolWorker-8] https://www.instagram.com:443 "GET /makemytrip HTTP/1.1" 301 0
[2025-07-27 05:39:18,192: DEBUG/ForkPoolWorker-8] https://www.instagram.com:443 "GET /makemytrip/ HTTP/1.1" 200 None
[2025-07-27 05:39:18,368: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with requests returned insufficient data
[2025-07-27 05:39:18,369: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting playwright extraction
{
  "url": "https://www.instagram.com/makemytrip",
  "method": "playwright",
  "attempt": 2
}
[2025-07-27 05:39:18,369: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:18][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.instagram.com/makemytrip",
  "timeout": 45
}
[2025-07-27 05:39:29,462: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:29][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.instagram.com/makemytrip"
}
[2025-07-27 05:39:29,462: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:29][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.instagram.com/makemytrip",
  "retry": 0
}
[2025-07-27 05:39:29,579: WARNING/ForkPoolWorker-8] [2025-07-27 05:39:29][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-27 05:40:04,433: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:04][text_extraction][NO_REF] ERROR: Failed to extract text with direct connection
{
  "error": "{'error': {'error': 'Page.goto: Timeout 30000ms exceeded.\\nCall log:\\n  - navigating to \"https://www.instagram.com/makemytrip\", waiting until \"load\"\\n', 'url': 'https://www.instagram.com/makemytrip', 'retry': 1}}"
}
[2025-07-27 05:40:04,434: WARNING/ForkPoolWorker-8] Traceback for analysis text_extraction:
[2025-07-27 05:40:04,435: WARNING/ForkPoolWorker-8] Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-27 05:40:04,564: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:04][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-27 05:40:04,565: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:04][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-27 05:40:06,370: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:06][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://www.instagram.com/makemytrip
[2025-07-27 05:40:06,371: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with playwright returned insufficient data
[2025-07-27 05:40:06,372: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:06][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: All text extraction methods failed for https://www.instagram.com/makemytrip
[2025-07-27 05:40:06,372: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:06][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://www.instagram.com/makemytrip: close_popups=True (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:40:06,373: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:06][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://www.instagram.com/makemytrip: 5000ms
[2025-07-27 05:40:12,146: ERROR/ForkPoolWorker-8] Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
[2025-07-27 05:40:12,147: INFO/ForkPoolWorker-8] Normalized URL: https://www.instagram.com/makemytrip -> https://www.instagram.com/makemytrip/
[2025-07-27 05:40:12,147: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://www.instagram.com/makemytrip/
[2025-07-27 05:40:12,556: INFO/ForkPoolWorker-8] Navigating to https://www.instagram.com/makemytrip/
[2025-07-27 05:40:15,602: INFO/ForkPoolWorker-8] Pressed Escape key to try closing popup
[2025-07-27 05:40:15,896: INFO/ForkPoolWorker-8] Handling Instagram-specific redirects and popups...
[2025-07-27 05:40:18,963: INFO/ForkPoolWorker-8] Current URL after initial load: https://www.instagram.com/makemytrip/
[2025-07-27 05:40:21,564: INFO/ForkPoolWorker-8] Final URL: https://www.instagram.com/makemytrip/
[2025-07-27 05:40:21,565: INFO/ForkPoolWorker-8] Successfully navigated past Instagram login requirements
[2025-07-27 05:40:23,874: INFO/ForkPoolWorker-8] Waiting 5000ms for images to load...
[2025-07-27 05:40:32,102: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:40:32,335: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:40:32,336: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:40:32,484: INFO/ForkPoolWorker-8] Screenshot captured in 20.34s, size: 16885 bytes
[2025-07-27 05:40:32,749: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:40:32,764: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png"
}
[2025-07-27 05:40:32,765: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '16885'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '365fd01a-6aac-11f0-a3ae-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:40:32,846: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:40:33 GMT'
    'Etag': '"0x8DDCCD01AF7977D"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '2f70513c-a01e-0069-78b8-fe8258000000'
    'x-ms-client-request-id': '365fd01a-6aac-11f0-a3ae-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:40:32 GMT'
[2025-07-27 05:40:32,847: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png"
}
[2025-07-27 05:40:32,847: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://www.instagram.com/makemytrip
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png"
}
[2025-07-27 05:40:32,848: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for instagram
{
  "url": "https://www.instagram.com/makemytrip",
  "text_length": 22,
  "screenshot_status": "success"
}
[2025-07-27 05:40:32,848: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for instagram
{
  "url": "https://www.instagram.com/makemytrip",
  "text_length": 0,
  "has_screenshot": true
}
[2025-07-27 05:40:32,849: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for facebook
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:40:32,850: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for facebook
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:40:32,850: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:40:32,851: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:32][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://www.facebook.com/makemytrip",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:40:32,851: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): www.facebook.com:443
[2025-07-27 05:40:33,112: DEBUG/ForkPoolWorker-8] https://www.facebook.com:443 "GET /makemytrip HTTP/1.1" 400 838
[2025-07-27 05:40:33,113: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:33][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Requests-based text extraction failed: 400 Client Error: Bad Request for url: https://www.facebook.com/makemytrip
[2025-07-27 05:40:33,114: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:33][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with requests returned insufficient data
[2025-07-27 05:40:33,114: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:33][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting playwright extraction
{
  "url": "https://www.facebook.com/makemytrip",
  "method": "playwright",
  "attempt": 2
}
[2025-07-27 05:40:33,115: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:33][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://www.facebook.com/makemytrip",
  "timeout": 45
}
[2025-07-27 05:40:43,946: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:43][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:40:43,947: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:43][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.facebook.com/makemytrip",
  "retry": 0
}
[2025-07-27 05:40:44,057: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:44][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-27 05:40:48,893: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:48][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://www.facebook.com/makemytrip"
}
[2025-07-27 05:40:48,894: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:48][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://www.facebook.com/makemytrip",
  "retry": 1
}
[2025-07-27 05:40:49,015: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:49][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-27 05:40:49,016: WARNING/ForkPoolWorker-8] [2025-07-27 05:40:49][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-27 05:41:21,147: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:21][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://www.facebook.com/makemytrip
[2025-07-27 05:41:21,148: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: Text extraction with playwright returned insufficient data
[2025-07-27 05:41:21,148: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:21][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] WARNING: All text extraction methods failed for https://www.facebook.com/makemytrip
[2025-07-27 05:41:21,149: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:21][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://www.facebook.com/makemytrip: close_popups=True (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:41:21,149: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:21][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://www.facebook.com/makemytrip: 4000ms
[2025-07-27 05:41:21,190: ERROR/ForkPoolWorker-8] Future exception was never retrieved
future: <Future finished exception=TargetClosedError('Target page, context or browser has been closed')>
playwright._impl._errors.TargetClosedError: Target page, context or browser has been closed
[2025-07-27 05:41:26,676: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://www.facebook.com/makemytrip
[2025-07-27 05:41:27,095: INFO/ForkPoolWorker-8] Navigating to https://www.facebook.com/makemytrip
[2025-07-27 05:41:30,132: INFO/ForkPoolWorker-8] Pressed Escape key to try closing popup
[2025-07-27 05:41:30,420: INFO/ForkPoolWorker-8] Handling Facebook-specific popups...
[2025-07-27 05:41:30,871: INFO/ForkPoolWorker-8] Waiting 4000ms for images to load...
[2025-07-27 05:41:37,873: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:41:38,099: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:41:38,099: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:41:38,278: INFO/ForkPoolWorker-8] Screenshot captured in 11.60s, size: 13630 bytes
[2025-07-27 05:41:38,527: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:41:38,539: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png"
}
[2025-07-27 05:41:38,541: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '13630'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '5d945ffa-6aac-11f0-b904-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:41:38,620: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:41:38 GMT'
    'Etag': '"0x8DDCCD041EC5469"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': 'e28c9420-601e-0014-08b9-fef37b000000'
    'x-ms-client-request-id': '5d945ffa-6aac-11f0-b904-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:41:38 GMT'
[2025-07-27 05:41:38,622: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png"
}
[2025-07-27 05:41:38,623: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://www.facebook.com/makemytrip
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png"
}
[2025-07-27 05:41:38,624: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for facebook
{
  "url": "https://www.facebook.com/makemytrip",
  "text_length": 22,
  "screenshot_status": "success"
}
[2025-07-27 05:41:38,625: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for facebook
{
  "url": "https://www.facebook.com/makemytrip",
  "text_length": 0,
  "has_screenshot": true
}
[2025-07-27 05:41:38,626: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside"
}
[2025-07-27 05:41:38,627: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside"
}
[2025-07-27 05:41:38,628: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://in.linkedin.com/company/makemytrip.com?open=outside"
}
[2025-07-27 05:41:38,629: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:38][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:41:38,630: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): in.linkedin.com:443
[2025-07-27 05:41:39,873: DEBUG/ForkPoolWorker-8] https://in.linkedin.com:443 "GET /company/makemytrip.com?open=outside HTTP/1.1" 200 40655
[2025-07-27 05:41:40,245: DEBUG/ForkPoolWorker-8] Encoding detection: utf_8 is most likely the one.
[2025-07-27 05:41:40,338: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:40][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "text_length": 16252,
  "method": "requests"
}
[2025-07-27 05:41:40,339: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:40][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://in.linkedin.com/company/makemytrip.com?open=outside: close_popups=False (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:41:40,339: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:40][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://in.linkedin.com/company/makemytrip.com?open=outside: 4000ms
[2025-07-27 05:41:46,085: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://in.linkedin.com/company/makemytrip.com?open=outside
[2025-07-27 05:41:46,495: INFO/ForkPoolWorker-8] Navigating to https://in.linkedin.com/company/makemytrip.com?open=outside
[2025-07-27 05:41:46,658: DEBUG/ForkPoolWorker-8] close.started
[2025-07-27 05:41:46,659: DEBUG/ForkPoolWorker-8] close.complete
[2025-07-27 05:41:48,394: INFO/ForkPoolWorker-8] Waiting 4000ms for images to load...
[2025-07-27 05:41:52,477: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:41:57,661: INFO/ForkPoolWorker-8] Network settle timeout (expected for some platforms): Timeout 5000ms exceeded.
[2025-07-27 05:41:57,662: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:41:59,204: INFO/ForkPoolWorker-8] Screenshot captured in 13.12s, size: 1788330 bytes
[2025-07-27 05:41:59,493: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:41:59,506: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png"
}
[2025-07-27 05:41:59,508: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '1788330'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '6a13b520-6aac-11f0-9922-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:41:59,715: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:42:01 GMT'
    'Etag': '"0x8DDCCD04FA38D8E"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': 'e5edce3e-301e-0036-0fb9-fe3664000000'
    'x-ms-client-request-id': '6a13b520-6aac-11f0-9922-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:42:01 GMT'
[2025-07-27 05:41:59,716: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png"
}
[2025-07-27 05:41:59,718: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://in.linkedin.com/company/makemytrip.com?open=outside
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png"
}
[2025-07-27 05:41:59,719: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "text_length": 16252,
  "screenshot_status": "success"
}
[2025-07-27 05:41:59,720: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for linkedin
{
  "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
  "text_length": 16252,
  "has_screenshot": true
}
[2025-07-27 05:41:59,721: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Extracting content for twitter
{
  "url": "https://x.com/makemytrip"
}
[2025-07-27 05:41:59,722: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting content extraction for twitter
{
  "url": "https://x.com/makemytrip"
}
[2025-07-27 05:41:59,723: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting robust text extraction
{
  "target_url": "https://x.com/makemytrip"
}
[2025-07-27 05:41:59,723: WARNING/ForkPoolWorker-8] [2025-07-27 05:41:59][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Attempting requests extraction
{
  "url": "https://x.com/makemytrip",
  "method": "requests",
  "attempt": 1
}
[2025-07-27 05:41:59,725: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): x.com:443
[2025-07-27 05:42:00,015: DEBUG/ForkPoolWorker-8] https://x.com:443 "GET /makemytrip HTTP/1.1" 200 None
[2025-07-27 05:42:00,077: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:00][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text extraction successful with requests
{
  "url": "https://x.com/makemytrip",
  "text_length": 492,
  "method": "requests"
}
[2025-07-27 05:42:00,078: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:00][run_screenshot][NO_REF] INFO: Auto-detected popup setting for https://x.com/makemytrip: close_popups=True (platforms with popups: ['instagram.com', 'facebook.com', 'fb.com', 'twitter.com', 'x.com', 'pinterest.com', 'pin.it'])
[2025-07-27 05:42:00,078: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:00][run_screenshot][NO_REF] INFO: Auto-detected image loading wait time for https://x.com/makemytrip: 3500ms
[2025-07-27 05:42:06,239: INFO/ForkPoolWorker-8] Starting OPTIMIZED screenshot capture for https://x.com/makemytrip
[2025-07-27 05:42:06,706: INFO/ForkPoolWorker-8] Navigating to https://x.com/makemytrip
[2025-07-27 05:42:13,066: INFO/ForkPoolWorker-8] Pressed Escape key to try closing popup
[2025-07-27 05:42:13,338: INFO/ForkPoolWorker-8] Handling X/Twitter-specific popups...
[2025-07-27 05:42:13,820: INFO/ForkPoolWorker-8] Waiting 3500ms for images to load...
[2025-07-27 05:42:17,378: INFO/ForkPoolWorker-8] Social media platform detected - waiting for network to settle...
[2025-07-27 05:42:17,579: INFO/ForkPoolWorker-8] Network settled for social media platform
[2025-07-27 05:42:17,579: INFO/ForkPoolWorker-8] Taking optimized screenshot
[2025-07-27 05:42:17,773: INFO/ForkPoolWorker-8] Screenshot captured in 11.53s, size: 27051 bytes
[2025-07-27 05:42:18,091: DEBUG/ForkPoolWorker-8] Browser closed successfully
[2025-07-27 05:42:18,108: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated timestamped filename
{
  "filename": "1753594938_c42850c0-bba5-4330-80e5-02ee611d76d0.png"
}
[2025-07-27 05:42:18,110: INFO/ForkPoolWorker-8] Request URL: 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594938_c42850c0-bba5-4330-80e5-02ee611d76d0.png'
Request method: 'PUT'
Request headers:
    'Content-Length': '27051'
    'x-ms-blob-type': 'REDACTED'
    'x-ms-version': 'REDACTED'
    'Content-Type': 'application/octet-stream'
    'Accept': 'application/xml'
    'User-Agent': 'azsdk-python-storage-blob/12.24.1 Python/3.12.3 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.39)'
    'x-ms-date': 'REDACTED'
    'x-ms-client-request-id': '752a2937-6aac-11f0-b641-00155d16c231'
    'Authorization': 'REDACTED'
A body is sent with the request
[2025-07-27 05:42:18,192: INFO/ForkPoolWorker-8] Response status: 201
Response headers:
    'Content-Length': '0'
    'Content-MD5': 'REDACTED'
    'Last-Modified': 'Sun, 27 Jul 2025 05:42:18 GMT'
    'Etag': '"0x8DDCCD059BE4D80"'
    'Server': 'Windows-Azure-Blob/1.0 Microsoft-HTTPAPI/2.0'
    'x-ms-request-id': '21d86704-a01e-001b-67b9-fe8517000000'
    'x-ms-client-request-id': '752a2937-6aac-11f0-b641-00155d16c231'
    'x-ms-version': 'REDACTED'
    'x-ms-content-crc64': 'REDACTED'
    'x-ms-request-server-encrypted': 'REDACTED'
    'x-ms-version-id': 'REDACTED'
    'Date': 'Sun, 27 Jul 2025 05:42:18 GMT'
[2025-07-27 05:42:18,193: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] DEBUG: Generated Azure URL
{
  "url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594938_c42850c0-bba5-4330-80e5-02ee611d76d0.png"
}
[2025-07-27 05:42:18,193: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Successfully uploaded screenshot to Azure for https://x.com/makemytrip
{
  "azure_url": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594938_c42850c0-bba5-4330-80e5-02ee611d76d0.png"
}
[2025-07-27 05:42:18,194: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for twitter
{
  "url": "https://x.com/makemytrip",
  "text_length": 492,
  "screenshot_status": "success"
}
[2025-07-27 05:42:18,195: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Content extraction completed for twitter
{
  "url": "https://x.com/makemytrip",
  "text_length": 492,
  "has_screenshot": true
}
[2025-07-27 05:42:18,195: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Content extraction completed for all categories
{
  "categories_processed": 13,
  "categories_with_content": 6
}
[2025-07-27 05:42:18,196: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Hard classification flow completed
{
  "categories_processed": 13
}
[2025-07-27 05:42:18,196: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:42:18,196: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 💾 STEP 4: SAVING ANALYSIS RESULTS
[2025-07-27 05:42:18,197: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ----------------------------------------
2025-07-27 05:42:18,206 INFO sqlalchemy.engine.Engine BEGIN (implicit)
[2025-07-27 05:42:18,206: INFO/ForkPoolWorker-8] BEGIN (implicit)
2025-07-27 05:42:18,207 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
[2025-07-27 05:42:18,207: INFO/ForkPoolWorker-8] SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.details, policy_analysis_new_gemini.org_id, policy_analysis_new_gemini.processing_status 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s
2025-07-27 05:42:18,208 INFO sqlalchemy.engine.Engine [cached since 663.9s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
[2025-07-27 05:42:18,208: INFO/ForkPoolWorker-8] [cached since 663.9s ago] {'scrape_request_ref_id_1': '428220ca-60c7-4af6-ab0e-799e4ede7379'}
2025-07-27 05:42:18,223 INFO sqlalchemy.engine.Engine UPDATE policy_analysis_new_gemini SET terms_and_condition_screenshot=%(terms_and_condition_screenshot)s, instagram_screenshot=%(instagram_screenshot)s, facebook_screenshot=%(facebook_screenshot)s, twitter_screenshot=%(twitter_screenshot)s, linkedin_screenshot=%(linkedin_screenshot)s, completed_at=%(completed_at)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
[2025-07-27 05:42:18,223: INFO/ForkPoolWorker-8] UPDATE policy_analysis_new_gemini SET terms_and_condition_screenshot=%(terms_and_condition_screenshot)s, instagram_screenshot=%(instagram_screenshot)s, facebook_screenshot=%(facebook_screenshot)s, twitter_screenshot=%(twitter_screenshot)s, linkedin_screenshot=%(linkedin_screenshot)s, completed_at=%(completed_at)s WHERE policy_analysis_new_gemini.id = %(policy_analysis_new_gemini_id)s
2025-07-27 05:42:18,224 INFO sqlalchemy.engine.Engine [generated in 0.00110s] {'terms_and_condition_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png', 'instagram_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png', 'facebook_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png', 'twitter_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594938_c42850c0-bba5-4330-80e5-02ee611d76d0.png', 'linkedin_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png', 'completed_at': '2025-07-27T05:42:18.222181Z', 'policy_analysis_new_gemini_id': 36}
[2025-07-27 05:42:18,224: INFO/ForkPoolWorker-8] [generated in 0.00110s] {'terms_and_condition_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png', 'instagram_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png', 'facebook_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png', 'twitter_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594938_c42850c0-bba5-4330-80e5-02ee611d76d0.png', 'linkedin_screenshot': 'https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png', 'completed_at': '2025-07-27T05:42:18.222181Z', 'policy_analysis_new_gemini_id': 36}
2025-07-27 05:42:18,236 INFO sqlalchemy.engine.Engine COMMIT
[2025-07-27 05:42:18,236: INFO/ForkPoolWorker-8] COMMIT
[2025-07-27 05:42:18,265: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Analysis results saved to database
{
  "categories_saved": 13,
  "analysis_flow": "normal",
  "reachability_percentage": 100.0
}
[2025-07-27 05:42:18,266: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: Analysis results saved successfully
[2025-07-27 05:42:18,266: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:42:18,267: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📤 STEP 5: SENDING WEBHOOK NOTIFICATION
[2025-07-27 05:42:18,268: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ----------------------------------------
[2025-07-27 05:42:18,269: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Starting webhook preparation
{
  "website": "https://www.makemytrip.com/",
  "content_data_keys": [
    "terms_and_condition",
    "contact_us",
    "instagram",
    "facebook",
    "linkedin",
    "twitter",
    "returns_cancellation_exchange",
    "x",
    "pinterest",
    "shipping_delivery",
    "youtube",
    "about_us",
    "privacy_policy"
  ],
  "content_data_size": 13,
  "content_data_details": {
    "terms_and_condition": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "contact_us": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "instagram": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "facebook": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "linkedin": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "twitter": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "returns_cancellation_exchange": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "x": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "pinterest": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "shipping_delivery": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "youtube": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "about_us": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    },
    "privacy_policy": {
      "has_url": true,
      "has_text": true,
      "has_screenshot": true
    }
  }
}
[2025-07-27 05:42:18,269: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📱 Collected Twitter data for merging
{
  "category": "twitter",
  "has_url": true,
  "has_text": true,
  "has_screenshot": true
}
[2025-07-27 05:42:18,270: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📱 Collected X data for merging
{
  "category": "x",
  "has_url": true,
  "has_text": true,
  "has_screenshot": true
}
[2025-07-27 05:42:18,270: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🚫 Excluding About Us (AU) category from webhook payload
{
  "category": "about_us",
  "reason": "excluded_by_requirement"
}
[2025-07-27 05:42:18,271: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🔄 Merging Twitter and X data - prioritizing X data
{
  "twitter_url": "https://x.com/makemytrip",
  "x_url": "not_found",
  "selected_source": "X"
}
[2025-07-27 05:42:18,271: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ Successfully merged Twitter/X data
{
  "merge_source": "X (prioritized over Twitter)",
  "final_category": "X",
  "url": "not_found"
}
[2025-07-27 05:42:18,272: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text truncated from 443 to 100 words for API compatibility
[2025-07-27 05:42:18,272: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text truncated from 445 to 100 words for API compatibility
[2025-07-27 05:42:18,273: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Text truncated from 2339 to 100 words for API compatibility
[2025-07-27 05:42:18,273: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📤 Final webhook payload policy types
{
  "policy_types": [
    "TNC",
    "CU",
    "IG",
    "FB",
    "LI",
    "RAC",
    "PT",
    "SD",
    "YT",
    "PP",
    "X"
  ],
  "total_policies": 11,
  "excluded_categories": [
    "AU"
  ],
  "merged_categories": "TW+X -> X"
}
[2025-07-27 05:42:18,274: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📤 WEBHOOK REQUEST PREPARATION - Enhanced Policy Service
{
  "webhook_url": "https://bffapi.biztel.ai/api/policy/results",
  "method": "PATCH",
  "headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_configured": true,
  "api_key_length": 8,
  "content_type": "application/json",
  "payload_size_bytes": 3782,
  "payload_size_kb": 3.69,
  "policies_count": 11,
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379",
  "website": "https://www.makemytrip.com/",
  "org_id": 1,
  "timestamp": "2025-07-27T05:42:18.274069"
}
[2025-07-27 05:42:18,274: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📋 WEBHOOK PAYLOAD STRUCTURE - Enhanced Policy Service
{
  "payload_structure": {
    "website": "https://www.makemytrip.com/",
    "scrapeRequestUuid": "428220ca-60c7-4af6-ab0e-799e4ede7379",
    "createdDate": "2025-07-27T05:42:18.273941Z",
    "status": "COMPLETED",
    "org_id": 1,
    "policies_count": 11,
    "policy_types": [
      "TNC",
      "CU",
      "IG",
      "FB",
      "LI",
      "RAC",
      "PT",
      "SD",
      "YT",
      "PP",
      "X"
    ],
    "policy_details": [
      {
        "type": "TNC",
        "has_url": true,
        "has_screenshot": true,
        "has_text": true,
        "url_preview": "https://promos.makemytrip.com/mmt-north-bus-tncs-2...",
        "text_length": 605
      },
      {
        "type": "CU",
        "has_url": true,
        "has_screenshot": false,
        "has_text": true,
        "url_preview": "https://www.makemytrip.com/support/contact-us.php",
        "text_length": 695
      },
      {
        "type": "IG",
        "has_url": true,
        "has_screenshot": true,
        "has_text": false,
        "url_preview": "https://www.instagram.com/makemytrip",
        "text_length": 22
      },
      {
        "type": "FB",
        "has_url": true,
        "has_screenshot": true,
        "has_text": false,
        "url_preview": "https://www.facebook.com/makemytrip",
        "text_length": 22
      },
      {
        "type": "LI",
        "has_url": true,
        "has_screenshot": true,
        "has_text": true,
        "url_preview": "https://in.linkedin.com/company/makemytrip.com?ope...",
        "text_length": 688
      },
      {
        "type": "RAC",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "PT",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "SD",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "YT",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "PP",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      },
      {
        "type": "X",
        "has_url": false,
        "has_screenshot": false,
        "has_text": false,
        "url_preview": "not_found",
        "text_length": 14
      }
    ]
  },
  "full_payload": {
    "website": "https://www.makemytrip.com/",
    "scrapeRequestUuid": "428220ca-60c7-4af6-ab0e-799e4ede7379",
    "createdDate": "2025-07-27T05:42:18.273941Z",
    "status": "COMPLETED",
    "policies": [
      {
        "type": "TNC",
        "url": "https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png",
        "text": "MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare (Pre-Tax Amount). The offer is not applicable on Government/ RTC buses. The offer will be applicable on booking amount exclusive of convenience fee, Insurance, Zero Cancellation Protection, Taxes & Ancillaries fee. The offer is not applicable on payments made through My Wallet (MakeMyTrip Wallet - bonus amount) and Gift card. Maximum discount limit: Rs. 300 Min booking price should be Rs. 200 How do you get it? To avail the offer customer must enter the"
      },
      {
        "type": "CU",
        "url": "https://www.makemytrip.com/support/contact-us.php",
        "imglink": "screenshot_failed",
        "text": "Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugram, Haryana 122002,India Fixed Line:(0124) 4628747 (0124) 5045105 Click for Directions Map Click here for our Retail Office Locations Existing Bookings For E-Tickets/Cancellations/Refund Status,please visit the My Trips section on App or Website For any other assistance, please contact our Customer Support service. For Flights / Hotels / Holidays(0124) 462 8747(0124) 5045105For Bus(0124) 4628765/(0124) 5045118 From all major operatorsFor Trains(For PNR enquiry & current status SMS to 139 : PNR <10 digit PNR number>)(For making your bookings"
      },
      {
        "type": "IG",
        "url": "https://www.instagram.com/makemytrip",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png",
        "text": "text_extraction_failed"
      },
      {
        "type": "FB",
        "url": "https://www.facebook.com/makemytrip",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png",
        "text": "text_extraction_failed"
      },
      {
        "type": "LI",
        "url": "https://in.linkedin.com/company/makemytrip.com?open=outside",
        "imglink": "https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png",
        "text": "MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Information and Internet Gurgaon, Harayana 918,628 followers See jobs Follow View all 6,884 employees Report this company Overview Jobs Life About us With our three powerhouse brands\u2014MakeMyTrip, Goibibo, and Redbus\u2014we are proud pioneers of online travel in India. We empower millions of travelers with easy and instant travel solutions, offering a wide range of services, including flights, hotels, homestays, holiday packages, cabs, buses, and trains. Our recent expansions include successful corporate travel solutions, a travel suite for agents,"
      },
      {
        "type": "RAC",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "PT",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "SD",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "YT",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "PP",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      },
      {
        "type": "X",
        "url": "not_found",
        "imglink": "not_applicable",
        "text": "not_applicable"
      }
    ],
    "org_id": 1
  }
}
[2025-07-27 05:42:18,275: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🚀 WEBHOOK REQUEST SENDING - Enhanced Policy Service
{
  "url": "https://bffapi.biztel.ai/api/policy/results",
  "method": "PATCH",
  "timeout": 30,
  "request_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "payload_size_kb": 3.69,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/policy/results' -H 'X-API-KEY: 12345678' -H 'Content-Type: application/json' -d '{\"website\": \"https://www.makemytrip.com/\", \"scrapeRequestUuid\": \"428220ca-60c7-4af6-ab0e-799e4ede7379\", \"createdDate\": \"2025-07-27T05:42:18.273941Z\", \"status\": \"COMPLETED\", \"policies\": [{\"type\": \"TNC\", \"url\": \"https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png\", \"text\": \"MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare (Pre-Tax Amount). The offer is not applicable on Government/ RTC buses. The offer will be applicable on booking amount exclusive of convenience fee, Insurance, Zero Cancellation Protection, Taxes & Ancillaries fee. The offer is not applicable on payments made through My Wallet (MakeMyTrip Wallet - bonus amount) and Gift card. Maximum discount limit: Rs. 300 Min booking price should be Rs. 200 How do you get it? To avail the offer customer must enter the\"}, {\"type\": \"CU\", \"url\": \"https://www.makemytrip.com/support/contact-us.php\", \"imglink\": \"screenshot_failed\", \"text\": \"Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugram, Haryana 122002,India Fixed Line:(0124) 4628747 (0124) 5045105 Click for Directions Map Click here for our Retail Office Locations Existing Bookings For E-Tickets/Cancellations/Refund Status,please visit the My Trips section on App or Website For any other assistance, please contact our Customer Support service. For Flights / Hotels / Holidays(0124) 462 8747(0124) 5045105For Bus(0124) 4628765/(0124) 5045118 From all major operatorsFor Trains(For PNR enquiry & current status SMS to 139 : PNR <10 digit PNR number>)(For making your bookings\"}, {\"type\": \"IG\", \"url\": \"https://www.instagram.com/makemytrip\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png\", \"text\": \"text_extraction_failed\"}, {\"type\": \"FB\", \"url\": \"https://www.facebook.com/makemytrip\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png\", \"text\": \"text_extraction_failed\"}, {\"type\": \"LI\", \"url\": \"https://in.linkedin.com/company/makemytrip.com?open=outside\", \"imglink\": \"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png\", \"text\": \"MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Information and Internet Gurgaon, Harayana 918,628 followers See jobs Follow View all 6,884 employees Report this company Overview Jobs Life About us With our three powerhouse brands\\u2014MakeMyTrip, Goibibo, and Redbus\\u2014we are proud pioneers of online travel in India. We empower millions of travelers with easy and instant travel solutions, offering a wide range of services, including flights, hotels, homestays, holiday packages, cabs, buses, and trains. Our recent expansions include successful corporate travel solutions, a travel suite for agents,\"}, {\"type\": \"RAC\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"PT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"SD\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"YT\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"PP\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}, {\"type\": \"X\", \"url\": \"not_found\", \"imglink\": \"not_applicable\", \"text\": \"not_applicable\"}], \"org_id\": 1}'",
  "timestamp": "2025-07-27T05:42:18.275410"
}
[2025-07-27 05:42:18,276: DEBUG/ForkPoolWorker-8] Starting new HTTPS connection (1): bffapi.biztel.ai:443
[2025-07-27 05:42:18,531: DEBUG/ForkPoolWorker-8] https://bffapi.biztel.ai:443 "PATCH /api/policy/results HTTP/1.1" 200 None
[2025-07-27 05:42:18,532: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📥 WEBHOOK RESPONSE RECEIVED - Enhanced Policy Service
{
  "status_code": 200,
  "success": true,
  "response_text": "{\"website\":\"https://www.makemytrip.com/\",\"createdDate\":\"2025-07-04T16:57:11.000+00:00\",\"scrapeRequestUuid\":\"428220ca-60c7-4af6-ab0e-799e4ede7379\",\"status\":\"COMPLETED\",\"policies\":[{\"type\":\"Terms\",\"url\":\"https://promos.makemytrip.com/mmt-north-bus-tncs-200524.html?detail_image=no\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594746_d16aca77-cc78-40b4-b613-a6c13ca4807d.png\",\"text\":\"MakeMyTrip Offer Details Coupon Code Category Offer Details MMTNORTH Bus Flat 8% OFF* What do you get? The Instant Discount is applied on Base Fare (Pre-Tax Amount). The offer is not applicable on Government/ RTC buses. The offer will be applicable on booking amount exclusive of convenience fee, Insurance, Zero Cancellation Protection, Taxes & Ancillaries fee. The offer is not applicable on payments made through My Wallet (MakeMyTrip Wallet - bonus amount) and Gift card. Maximum discount limit: Rs. 300 Min booking price should be Rs. 200 How do you get it? To avail the offer customer must enter the\"},{\"type\":\"Contact_page\",\"url\":\"https://www.makemytrip.com/support/contact-us.php\",\"imglink\":\"screenshot_failed\",\"text\":\"Support Contact Us Contact Us Gurgaon (Head Office) MakeMyTrip India Pvt. Ltd.,DLF Building No. 5 Tower BDLF Cyber City, DLF Phase 2Sector 25, Gurugram, Haryana 122002,India Fixed Line:(0124) 4628747 (0124) 5045105 Click for Directions Map Click here for our Retail Office Locations Existing Bookings For E-Tickets/Cancellations/Refund Status,please visit the My Trips section on App or Website For any other assistance, please contact our Customer Support service. For Flights / Hotels / Holidays(0124) 462 8747(0124) 5045105For Bus(0124) 4628765/(0124) 5045118 From all major operatorsFor Trains(For PNR enquiry & current status SMS to 139 : PNR <10 digit PNR number>)(For making your bookings\"},{\"type\":\"Instagram\",\"url\":\"https://www.instagram.com/makemytrip\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594832_d406c7f7-e9d1-4c8b-a57a-0951a5be0d67.png\",\"text\":\"text_extraction_failed\"},{\"type\":\"Facebook\",\"url\":\"https://www.facebook.com/makemytrip\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594898_24588d19-91b3-4296-8ccc-4eda1a990a89.png\",\"text\":\"text_extraction_failed\"},{\"type\":\"Linkedln\",\"url\":\"https://in.linkedin.com/company/makemytrip.com?open=outside\",\"imglink\":\"https://bizintelblobs.blob.core.windows.net/screenshots-dsapi/1753594919_59b75e14-9b05-4d35-917a-2eb3fba585e3.png\",\"text\":\"MakeMyTrip | LinkedIn Skip to main content LinkedIn Articles People Learning Jobs Games Get the app Join now Sign in MakeMyTrip Technology, Information and Internet Gurgaon, Harayana 918,628 followers See jobs Follow View all 6,884 employees Report this company Overview Jobs Life About us With our three powerhouse brands\u2014MakeMyTrip, Goibibo, and Redbus\u2014we are proud pioneers of online travel in India. We empower millions of travelers with easy and instant travel solutions, offering a wide range of services, including flights, hotels, homestays, holiday packages, cabs, buses, and trains. Our recent expansions include successful corporate travel solutions, a travel suite for agents,\"},{\"type\":\"Refund_cancellation_policy\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Pinterest\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Shipping_delivery_policy\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Youtube\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"Privacy_policy\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"},{\"type\":\"X\",\"url\":\"not_found\",\"imglink\":\"not_applicable\",\"text\":\"not_applicable\"}],\"registeredEntity\":null,\"processingStatus\":\"INITIATED\",\"org_id\":2}",
  "response_headers": {
    "Server": "nginx/1.22.1",
    "Date": "Sun, 27 Jul 2025 05:42:19 GMT",
    "Content-Type": "application/json",
    "Transfer-Encoding": "chunked",
    "Connection": "keep-alive",
    "Vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "X-Content-Type-Options": "nosniff",
    "X-XSS-Protection": "0",
    "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate",
    "Pragma": "no-cache",
    "Expires": "0",
    "X-Frame-Options": "DENY"
  },
  "response_size_bytes": 3835,
  "response_size_kb": 3.75,
  "url": "https://bffapi.biztel.ai/api/policy/results",
  "payload_size_kb": 3.69,
  "request_duration_ms": 255.89,
  "timestamp": "2025-07-27T05:42:18.532417"
}
[2025-07-27 05:42:18,533: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ WEBHOOK SUCCESS - Enhanced Policy Service
{
  "status_code": 200,
  "response": "{\"website\":\"https://www.makemytrip.com/\",\"createdDate\":\"2025-07-04T16:57:11.000+00:00\",\"scrapeRequestUuid\":\"428220ca-60c7-4af6-ab0e-799e4ede7379\",\"status\":\"COMPLETED\",\"policies\":[{\"type\":\"Terms\",\"url\"",
  "policies_sent": 11,
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379"
}
[2025-07-27 05:42:18,533: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ✅ RESULT: Webhook notification sent
[2025-07-27 05:42:18,534: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:42:18,535: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 📊 EXTRACTION SUMMARY:
[2025-07-27 05:42:18,535: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📋 Total categories: 13
[2025-07-27 05:42:18,536: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📝 Successful text extractions: 4
[2025-07-27 05:42:18,536: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    📸 Successful screenshots: 5
[2025-07-27 05:42:18,537: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    🔗 Categories with URLs: 6
[2025-07-27 05:42:18,537: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO:    ⏱️ Processing time: 206.02s
[2025-07-27 05:42:18,538: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 
[2025-07-27 05:42:18,539: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: 🎉 ENHANCED POLICY ANALYSIS COMPLETED SUCCESSFULLY!
[2025-07-27 05:42:18,539: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: ================================================================================
[2025-07-27 05:42:18,540: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][428220ca-60c7-4af6-ab0e-799e4ede7379][NO_REF] INFO: Enhanced policy analysis completed successfully
{
  "status": "COMPLETED",
  "scrape_request_ref_id": "428220ca-60c7-4af6-ab0e-799e4ede7379",
  "website": "https://www.makemytrip.com/",
  "analysis_flow": "normal",
  "reachability_percentage": 100.0,
  "categories_processed": 13,
  "processing_time": 206.01873135566711,
  "webhook_sent": true,
  "unified_classification": true
}
[2025-07-27 05:42:18,542: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: Enhanced Policy Analysis task completed in 206.13 seconds with status: COMPLETED
[2025-07-27 05:42:18,542: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: 
[2025-07-27 05:42:18,543: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: 📤 SENDING POLICY RESULTS WEBHOOK
[2025-07-27 05:42:18,543: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: ==================================================
[2025-07-27 05:42:18,544: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: ✅ Sending successful policy analysis webhook
[2025-07-27 05:42:18,544: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: ✅ SUCCESS: Policy results webhook sent successfully
[2025-07-27 05:42:18,545: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][996][355dfa3a-8864-41ea-b57d-0f30f960604e] INFO: ==================================================
[2025-07-27 05:42:18,545: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][process_cleanup][NO_REF] INFO: Running Celery task cleanup
[2025-07-27 05:42:18,548: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][process_cleanup][NO_REF] DEBUG: No Playwright processes found to clean up
[2025-07-27 05:42:18,549: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][process_cleanup][NO_REF] WARNING: Could not clean up async tasks: There is no current event loop in thread 'MainThread'.
[2025-07-27 05:42:18,549: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][process_cleanup][NO_REF] INFO: Celery task cleanup completed
[2025-07-27 05:42:18,551: WARNING/ForkPoolWorker-8] [2025-07-27 05:42:18][process_cleanup][NO_REF] DEBUG: No Playwright processes found to clean up
[2025-07-27 05:42:18,553: INFO/ForkPoolWorker-8] Task process_policy_analysis_enhanced[18f1a148-ca28-4fb6-a396-eacdd37e7093] succeeded in 188.54136263400005s: {'status': 'COMPLETED', 'task_id': '18f1a148-ca28-4fb6-a396-eacdd37e7093', 'website': 'https://makemytrip.com', 'scrape_request_ref_id': '428220ca-60c7-4af6-ab0e-799e4ede7379', 'analysis_id': 996, 'execution_time': 206.12705278396606, 'analysis_flow': 'normal', 'reachability_percentage': 100.0, 'categories_processed': 13, 'webhook_sent': True}

worker: Warm shutdown (MainProcess)
[2025-07-27 06:02:26,395: DEBUG/MainProcess] | Worker: Closing Hub...
[2025-07-27 06:02:26,396: DEBUG/MainProcess] | Worker: Closing Pool...
[2025-07-27 06:02:26,397: DEBUG/MainProcess] | Worker: Closing Consumer...
[2025-07-27 06:02:26,397: DEBUG/MainProcess] | Worker: Stopping Consumer...
[2025-07-27 06:02:26,398: DEBUG/MainProcess] | Consumer: Closing Connection...
[2025-07-27 06:02:26,399: DEBUG/MainProcess] | Consumer: Closing Events...
[2025-07-27 06:02:26,399: DEBUG/MainProcess] | Consumer: Closing Mingle...
[2025-07-27 06:02:26,399: DEBUG/MainProcess] | Consumer: Closing Gossip...
[2025-07-27 06:02:26,400: DEBUG/MainProcess] | Consumer: Closing Heart...
[2025-07-27 06:02:26,400: DEBUG/MainProcess] | Consumer: Closing Tasks...
[2025-07-27 06:02:26,401: DEBUG/MainProcess] | Consumer: Closing Control...
[2025-07-27 06:02:26,401: DEBUG/MainProcess] | Consumer: Closing event loop...
[2025-07-27 06:02:26,401: DEBUG/MainProcess] | Consumer: Stopping event loop...
[2025-07-27 06:02:26,402: DEBUG/MainProcess] | Consumer: Stopping Control...
[2025-07-27 06:02:26,407: DEBUG/MainProcess] | Consumer: Stopping Tasks...
[2025-07-27 06:02:26,407: DEBUG/MainProcess] Canceling task consumer...
[2025-07-27 06:02:26,408: DEBUG/MainProcess] | Consumer: Stopping Heart...
[2025-07-27 06:02:26,409: DEBUG/MainProcess] | Consumer: Stopping Gossip...
[2025-07-27 06:02:26,412: DEBUG/MainProcess] | Consumer: Stopping Mingle...
[2025-07-27 06:02:26,412: DEBUG/MainProcess] | Consumer: Stopping Events...
[2025-07-27 06:02:26,413: DEBUG/MainProcess] | Consumer: Stopping Connection...
[2025-07-27 06:02:26,413: DEBUG/MainProcess] | Worker: Stopping Pool...
[2025-07-27 06:02:27,406: DEBUG/MainProcess] | Worker: Stopping Hub...
[2025-07-27 06:02:27,406: DEBUG/MainProcess] | Consumer: Shutdown Control...
[2025-07-27 06:02:27,407: DEBUG/MainProcess] | Consumer: Shutdown Tasks...
[2025-07-27 06:02:27,407: DEBUG/MainProcess] Canceling task consumer...
[2025-07-27 06:02:27,408: DEBUG/MainProcess] Closing consumer channel...
[2025-07-27 06:02:27,408: DEBUG/MainProcess] | Consumer: Shutdown Heart...
[2025-07-27 06:02:27,409: DEBUG/MainProcess] | Consumer: Shutdown Gossip...
[2025-07-27 06:02:27,410: DEBUG/MainProcess] | Consumer: Shutdown Events...
[2025-07-27 06:02:27,411: DEBUG/MainProcess] | Consumer: Shutdown Connection...
[2025-07-27 06:02:27,413: DEBUG/MainProcess] removing tasks from inqueue until task handler finished
